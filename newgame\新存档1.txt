<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>人生游戏计划</title>
    <!-- 引入 Chart.js，用于绘制图表 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 全局样式 */
        body {
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #eef2f7;
            color: #333;
        }
        header {
            background: linear-gradient(90deg, #0072ff 0%, #00c6ff 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        header .left {
            display: flex;
            flex-direction: column;
        }
        header .left .datetime {
            font-size: 1.2em;
        }
        header .left .days {
            font-size: 0.9em;
            margin-top: 4px;
        }
        header .right button {
            margin-left: 10px;
            padding: 8px 16px;
            font-size: 0.9em;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            background-color: white;
            color: #0072ff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
            transition: background-color 0.3s, transform 0.2s;
        }
        header .right button:hover {
            background-color: #d0e7ff;
            transform: scale(1.05);
        }
        .container {
            padding: 20px;
        }
        /* 选项卡导航 */
        .tabs {
            display: flex;
            border-bottom: 2px solid #ddd;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            margin-right: 4px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            background-color: #eee;
            transition: background-color 0.2s;
        }
        .tab.active {
            background-color: white;
            border: 1px solid #ddd;
            border-bottom: none;
            font-weight: bold;
        }
        .tab:hover {
            background-color: #ddd;
        }
        /* 选项卡内容 */
        .tab-content {
            display: none;
            background-color: white;
            padding: 20px;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 4px 4px 4px;
        }
        .tab-content.active {
            display: block;
        }
        /* 現狀區域 */
        .status-card {
            background-color: white;
            padding: 20px;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            position: relative;
        }
        .status-card h2 {
            margin-top: 0;
        }
        .status-card .question-mark {
            position: absolute;
            top: 20px;
            right: 20px;
            cursor: pointer;
            font-size: 1.2em;
            color: #0072ff;
        }
        /* 弹出框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0; top: 0;
            width: 100%; height: 100%;
            background-color: rgba(0,0,0,0.4);
            justify-content: center;
            align-items: center;
        }
        .modal-content {
            background-color: white;
            width: 80%;
            max-height: 80%;
            overflow-y: auto;
            border-radius: 4px;
            padding: 20px;
            position: relative;
        }
        .modal-close {
            position: absolute;
            top: 12px;
            right: 12px;
            cursor: pointer;
            font-size: 1.2em;
            color: #888;
        }
        .modal-close:hover {
            color: #333;
        }
        /* 表单与表格样式 */
        form {
            margin-bottom: 20px;
        }
        form .form-group {
            margin-bottom: 12px;
        }
        form .form-group label {
            display: block;
            margin-bottom: 4px;
        }
        form .form-group input[type="number"],
        form .form-group input[type="text"],
        form .form-group input[type="date"],
        form .form-group textarea,
        form .form-group select {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }
        form .form-inline {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }
        form .form-inline .form-group {
            flex: 1 1 200px;
            margin-right: 10px;
        }
        form .form-inline .form-group:last-child {
            margin-right: 0;
        }
        form button {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            background-color: #0072ff;
            color: white;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        form button:hover {
            background-color: #005bb5;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            table-layout: auto;
        }
        table th, table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
            font-size: 0.9em;
        }
        table th {
            background-color: #f0f0f0;
        }
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 10px;
        }
        .pagination button {
            margin: 0 3px;
            padding: 4px 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: white;
            cursor: pointer;
        }
        .pagination button.active {
            background-color: #0072ff;
            color: white;
            border-color: #0072ff;
        }
        .pagination button:hover {
            background-color: #ddd;
        }
        /* 进度条样式 */
        .progress-container {
            background-color: #eee;
            border-radius: 4px;
            overflow: hidden;
            height: 16px;
        }
        .progress-bar {
            height: 100%;
            background-color: #0072ff;
            width: 0%;
            transition: width 0.3s;
        }
        /* 对话框提示文字 */
        .tooltip {
            position: absolute;
            background-color: #333;
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 0.85em;
            white-space: nowrap;
            display: none;
            z-index: 1001;
        }
        /* 切换按钮 */
        .toggle-btn {
            margin-bottom: 10px;
        }
        /* 卡片样式 */
        .card {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: box-shadow 0.3s;
        }
        .card:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
        }
        /* 按钮组 */
        .btn-group {
            display: flex;
        }
        .btn-group button {
            margin-right: 10px;
        }
        /* 表单中可删除条目按钮 */
        .remove-btn {
            background-color: #e74c3c;
        }
        .remove-btn:hover {
            background-color: #c0392b;
        }
        /* 页脚留白，避免遮挡 */
        .spacer {
            height: 60px;
        }
        /* 任务系统表格样式 */
        .task-table th, .task-table td {
            font-size: 0.85em;
        }
        .task-table .action-btn {
            margin: 0 2px;
            padding: 4px 8px;
            font-size: 0.8em;
        }
        .task-table .complete-btn {
            background-color: #27ae60;
        }
        .task-table .complete-btn:hover {
            background-color: #1e8449;
        }
        .task-table .delete-btn {
            background-color: #e74c3c;
        }
        .task-table .delete-btn:hover {
            background-color: #c0392b;
        }
    </style>
</head>
<body>
    <header>
        <div class="left">
            <div class="datetime" id="currentDatetime">加载中...</div>
            <div class="days" id="planDays">计划启动天数：0</div>
        </div>
        <div class="right">
            <button id="importBtn">导入数据</button>
            <button id="exportBtn">导出数据</button>
            <button id="saveBtn">保存</button>
            <button id="resetBtn">重置</button>
        </div>
    </header>
    <div class="container">
        <!-- 当前状态区域 -->
        <div class="status-card">
            <h2>当前状态 <span class="question-mark" id="statusInfoBtn">?</span></h2>
            <div id="statusContent">
                <!-- 动态渲染属性、职业水平、称号 -->
                <div class="card">
                    <h3>属性</h3>
                    <p>智力：<span id="attrInt">0</span></p>
                    <p>知识：<span id="attrKnowledge">0</span></p>
                    <p>阅识：<span id="attrReadExp">0</span></p>
                    <p>体力：<span id="attrStamina">0</span></p>
                    <p>意志：<span id="attrWill">0</span></p>
                    <p>魅力：<span id="attrCharisma">0</span></p>
                    <p>幻构师经验：<span id="attrArtExp">1460</span></p>
                </div>
                <div class="card">
                    <h3>职业水平</h3>
                    <p>幻构师等级：<span id="careerArtLevel">Lv.1 描形学徒（初级）</span></p>
                    <p>真理之路（知识侧）等级：<span id="careerTruthKnowledge">LV.1 灰袍学徒（初级）</span></p>
                    <p>真理之路（智力侧）等级：<span id="careerTruthIntelligence">LV.1 褐衣明理（初级）</span></p>
                </div>
                <div class="card">
                    <h3>称号</h3>
                    <p>晨曦之约称号：<span id="titleDawn">无</span></p>
                    <p>意志称号：<span id="titleWill">无</span></p>
                    <p>魅力称号：<span id="titleCharisma">无</span></p>
                    <p>阅识称号：<span id="titleReadExp">无</span></p>
                </div>
            </div>
        </div>

        <!-- 选项卡导航 -->
        <div class="tabs">
            <div class="tab active" data-tab="dailyRecordTab">每日记录</div>
            <div class="tab" data-tab="dawnTab">晨曦之约计划</div>
            <div class="tab" data-tab="dailySummaryTab">每日个人总结</div>
            <div class="tab" data-tab="dailyOverallTab">每日总记录</div>
            <div class="tab" data-tab="artPlanTab">幻构师计划</div>
            <div class="tab" data-tab="truthPlanTab">真理之路计划</div>
            <div class="tab" data-tab="titlesTab">称号系统</div>
            <div class="tab" data-tab="periodicSummaryTab">阶段性总结</div>
            <div class="tab" data-tab="tasksTab">任务系统</div>
        </div>

        <!-- “每日记录” 选项卡内容 -->
        <div class="tab-content active" id="dailyRecordTab">
            <form id="dailyRecordForm">
                <div class="form-inline">
                    <div class="form-group">
                        <label>绘画时长（分钟）</label>
                        <input type="number" id="inputArtMinutes" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>学习时长（分钟）</label>
                        <input type="number" id="inputStudyMinutes" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>阅读专著页数</label>
                        <input type="number" id="inputSpecialReadPages" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>运动时长（分钟）</label>
                        <input type="number" id="inputExerciseMinutes" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>阅书时长（分钟）</label>
                        <input type="number" id="inputBookReadMinutes" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>阅影时长（分钟）</label>
                        <input type="number" id="inputVideoWatchMinutes" min="0" value="0">
                    </div>
                </div>
                <div class="form-inline">
                    <div class="form-group">
                        <input type="checkbox" id="chkWillArt">
                        <label for="chkWillArt">绘画参与意志计算</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkWillStudy">
                        <label for="chkWillStudy">学习参与意志计算</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkWillExercise">
                        <label for="chkWillExercise">运动参与意志计算</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkWillBook">
                        <label for="chkWillBook">阅书参与意志计算</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkWillVideo">
                        <label for="chkWillVideo">阅影参与意志计算</label>
                    </div>
                </div>
                <button type="button" id="addDailyRecordBtn">添加记录</button>
            </form>
            <div>
                <button id="toggleDailyHistoryBtn">历史记录</button>
            </div>
            <div id="dailyHistoryContainer">
                <table id="dailyHistoryTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>学习时长</th>
                            <th>阅读专著页数</th>
                            <th>绘画时长</th>
                            <th>阅书时长</th>
                            <th>阅影时长</th>
                            <th>幻构师经验增量</th>
                            <th>智力增量</th>
                            <th>知识增量</th>
                            <th>体力增量</th>
                            <th>意志增量</th>
                            <th>魅力增量</th>
                            <th>称号奖励加成</th>
                            <th>连续成功/失败时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="dailyHistoryBody">
                        <!-- 动态插入记录行 -->
                    </tbody>
                </table>
                <div class="pagination" id="dailyHistoryPagination"></div>
            </div>
        </div>

        <!-- “晨曦之约计划” 选项卡内容 -->
        <div class="tab-content" id="dawnTab">
            <form id="dawnForm">
                <div class="form-group">
                    <input type="checkbox" id="chkSleptOnTime">
                    <label for="chkSleptOnTime">前一天及时入睡</label>
                </div>
                <div class="form-group">
                    <input type="checkbox" id="chkWokeOnTime">
                    <label for="chkWokeOnTime">当天及时起床</label>
                </div>
                <div class="form-group">
                    <input type="checkbox" id="chkSpecialCase">
                    <label for="chkSpecialCase">特殊情况（今日无需达成亦计为成功）</label>
                </div>
                <div class="form-inline">
                    <div class="form-group">
                        <input type="checkbox" id="chkEarlySleep">
                        <label for="chkEarlySleep">早睡（额外+1意志）</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkEarlyRise">
                        <label for="chkEarlyRise">早起（额外+0.5体力）</label>
                    </div>
                </div>
                <button type="button" id="addDawnRecordBtn">打卡</button>
            </form>
            <div>
                <button id="toggleDawnHistoryBtn">历史记录</button>
            </div>
            <div id="dawnHistoryContainer">
                <table id="dawnHistoryTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>打卡结果</th>
                            <th>早睡</th>
                            <th>早起</th>
                            <th>意志增量</th>
                            <th>体力增量</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="dawnHistoryBody">
                        <!-- 动态插入记录行 -->
                    </tbody>
                </table>
                <div class="pagination" id="dawnHistoryPagination"></div>
            </div>
        </div>

        <!-- “每日个人总结” 选项卡内容 -->
        <div class="tab-content" id="dailySummaryTab">
            <form id="dailySummaryForm">
                <div class="form-group">
                    <label>日期</label>
                    <input type="date" id="inputSummaryDate" required>
                </div>
                <div class="form-group">
                    <label>总结内容</label>
                    <textarea id="inputSummaryContent" rows="3"></textarea>
                </div>
                <button type="button" id="addDailySummaryBtn">保存总结</button>
            </form>
            <div>
                <button id="toggleSummaryHistoryBtn">历史记录</button>
            </div>
            <div id="summaryHistoryContainer">
                <table id="summaryHistoryTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>总结内容</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="summaryHistoryBody">
                        <!-- 动态插入记录行 -->
                    </tbody>
                </table>
                <div class="pagination" id="summaryHistoryPagination"></div>
            </div>
        </div>

        <!-- “每日总记录” 选项卡内容 -->
        <div class="tab-content" id="dailyOverallTab">
            <div>
                <button id="toggleOverallHistoryBtn">历史记录</button>
            </div>
            <div id="overallHistoryContainer">
                <table id="overallHistoryTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>幻构师进度（当前/增减）</th>
                            <th>知识侧进度（当前/增减）</th>
                            <th>智力侧进度（当前/增减）</th>
                            <th>晨曦打卡</th>
                            <th>每日总结</th>
                            <th>任务状态概览</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="overallHistoryBody">
                        <!-- 动态插入记录行 -->
                    </tbody>
                </table>
                <div class="pagination" id="overallHistoryPagination"></div>
            </div>
        </div>

        <!-- “幻构师计划” 选项卡内容 -->
        <div class="tab-content" id="artPlanTab">
            <div class="card">
                <h3>幻构师等级进度</h3>
                <table id="artPlanTable">
                    <thead>
                        <tr>
                            <th>等级</th>
                            <th>总需求经验</th>
                            <th>阶段</th>
                            <th>当前经验</th>
                            <th>进度</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态插入各等级行 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- “真理之路计划” 选项卡内容 -->
        <div class="tab-content" id="truthPlanTab">
            <div class="card">
                <h3>知识侧等级进度</h3>
                <table id="truthKnowledgeTable">
                    <thead>
                        <tr>
                            <th>等级</th>
                            <th>总需求知识</th>
                            <th>阶段</th>
                            <th>当前知识</th>
                            <th>进度</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态插入各等级行 -->
                    </tbody>
                </table>
            </div>
            <div class="card">
                <h3>智力侧等级进度</h3>
                <table id="truthIntelligenceTable">
                    <thead>
                        <tr>
                            <th>等级</th>
                            <th>总需求智力</th>
                            <th>阶段</th>
                            <th>当前智力</th>
                            <th>进度</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态插入各等级行 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- “称号系统” 选项卡内容 -->
        <div class="tab-content" id="titlesTab">
            <div class="card">
                <h3>晨曦之约称号</h3>
                <table id="titlesDawnTable">
                    <thead>
                        <tr>
                            <th>等级</th>
                            <th>称号</th>
                            <th>所需坚持天数</th>
                            <th>当前坚持天数</th>
                            <th>奖励</th>
                            <th>进度</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态插入各称号行 -->
                    </tbody>
                </table>
            </div>
            <div class="card">
                <h3>意志称号</h3>
                <table id="titlesWillTable">
                    <thead>
                        <tr>
                            <th>等级</th>
                            <th>称号</th>
                            <th>所需意志值</th>
                            <th>当前意志值</th>
                            <th>奖励</th>
                            <th>进度</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态插入各称号行 -->
                    </tbody>
                </table>
            </div>
            <div class="card">
                <h3>魅力称号</h3>
                <table id="titlesCharismaTable">
                    <thead>
                        <tr>
                            <th>等级</th>
                            <th>称号</th>
                            <th>所需魅力值</th>
                            <th>当前魅力值</th>
                            <th>奖励</th>
                            <th>进度</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态插入各称号行 -->
                    </tbody>
                </table>
            </div>
            <div class="card">
                <h3>阅识称号</h3>
                <table id="titlesReadExpTable">
                    <thead>
                        <tr>
                            <th>等级</th>
                            <th>称号</th>
                            <th>所需阅识值</th>
                            <th>当前阅识值</th>
                            <th>奖励</th>
                            <th>进度</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 动态插入各称号行 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- “阶段性总结” 选项卡内容 -->
        <div class="tab-content" id="periodicSummaryTab">
            <div class="card">
                <h3>基础属性记录</h3>
                <div class="toggle-btn">
                    <button id="toggleBasicMonthly">当月（日为单位）</button>
                    <button id="toggleBasicYearly">当年（日/月可切换）</button>
                    <button id="toggleBasicAll">过往（月/年可切换）</button>
                </div>
                <div style="display: flex; gap: 20px; flex-wrap: wrap;">
                    <div style="flex: 1; min-width: 300px;">
                        <canvas id="basicMonthlyChart"></canvas>
                    </div>
                    <div style="flex: 1; min-width: 300px;">
                        <canvas id="basicYearlyChart"></canvas>
                    </div>
                    <div style="flex: 1; min-width: 300px;">
                        <canvas id="basicAllChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="card">
                <h3>幻构师经验记录</h3>
                <div class="toggle-btn">
                    <button id="toggleArtMonthly">当月（日为单位）</button>
                    <button id="toggleArtYearly">当年（日/月可切换）</button>
                    <button id="toggleArtAll">过往（月/年可切换）</button>
                </div>
                <div style="display: flex; gap: 20px; flex-wrap: wrap;">
                    <div style="flex: 1; min-width: 300px;">
                        <canvas id="artMonthlyChart"></canvas>
                    </div>
                    <div style="flex: 1; min-width: 300px;">
                        <canvas id="artYearlyChart"></canvas>
                    </div>
                    <div style="flex: 1; min-width: 300px;">
                        <canvas id="artAllChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- “任务系统” 选项卡内容 -->
        <div class="tab-content" id="tasksTab">
            <div class="card">
                <h3>创建新任务</h3>
                <form id="taskForm">
                    <div class="form-group">
                        <label>名称</label>
                        <input type="text" id="taskName" required>
                    </div>
                    <div class="form-group">
                        <label>描述</label>
                        <textarea id="taskDescription" rows="2"></textarea>
                    </div>
                    <div class="form-group">
                        <label>类型</label>
                        <select id="taskType" required>
                            <option value="">请选择</option>
                            <option value="art">幻构师计划</option>
                            <option value="truth">真理之路计划</option>
                            <option value="dawn">晨曦之约计划</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>周期</label>
                        <select id="taskCycle" required>
                            <option value="">请选择</option>
                            <option value="short">短期</option>
                            <option value="long">长期</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>目标类型</label>
                        <select id="taskTargetType" required>
                            <option value="">请选择</option>
                            <option value="study">学习时长（分钟）</option>
                            <option value="specialRead">阅读页数（页）</option>
                            <option value="art">绘画时长（分钟）</option>
                            <option value="exercise">运动时长（分钟）</option>
                            <option value="bookRead">阅书时长（分钟）</option>
                            <option value="videoWatch">阅影时长（分钟）</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>目标数值</label>
                        <input type="number" id="taskTargetValue" min="1" required>
                    </div>
                    <div class="form-group">
                        <label>截止日期</label>
                        <input type="date" id="taskDeadline">
                    </div>
                    <div id="penaltyContainer">
                        <h4>惩罚</h4>
                        <button type="button" id="addPenaltyBtn">添加惩罚</button>
                        <!-- 惩罚条目将动态插入 -->
                    </div>
                    <div id="rewardContainer">
                        <h4>奖励</h4>
                        <button type="button" id="addRewardBtn">添加奖励</button>
                        <!-- 奖励条目将动态插入 -->
                    </div>
                    <button type="button" id="createTaskBtn">创建任务</button>
                </form>
            </div>
            <div class="card">
                <h3>未完成任务</h3>
                <table class="task-table" id="pendingTasksTable">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>类型</th>
                            <th>目标/当前进度</th>
                            <th>剩余天数</th>
                            <th>奖励/惩罚</th>
                            <th>进度</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="pendingTasksBody">
                        <!-- 动态插入未完成任务行 -->
                    </tbody>
                </table>
            </div>
            <div class="card">
                <h3>已完成任务</h3>
                <table class="task-table" id="completedTasksTable">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>类型</th>
                            <th>目标/完成进度</th>
                            <th>完成日期</th>
                            <th>奖励/惩罚</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="completedTasksBody">
                        <!-- 动态插入已完成任务行 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 规则说明弹出框 -->
    <div class="modal" id="statusInfoModal">
        <div class="modal-content">
            <span class="modal-close" id="statusInfoClose">&times;</span>
            <h2>基础属性提升规则 &amp; 称号获取规则</h2>
            <h3>基础属性提升方法</h3>
            <ul>
                <li>智力（Intelligence）：每学习 1 小时，增加 1 智力。</li>
                <li>体力（Stamina）：每次进行 1 小时的训练或运动，增加 1 体力。</li>
                <li>知识（Knowledge）：每阅读1页专著书籍，提升 0.1 知识。</li>
                <li>阅识（ReadExp）：每阅读1小时其它书籍，提升1阅识；每观看1小时影视，提升1阅识。</li>
                <li>意志（Willpower）：任何行动连续执行 3 天以上，各自每天增加 1 意志。连续执行 7 天以上，各自每天增加 2 意志。连续执行 30 天以上，各自每天增加 3 意志。不同行动获得或失去的意志分开计算，可叠加。任何行动执行失败1 天，各自减少 1 意志/天；连续失败≥3天，减少 2 意志/天；连续失败≥7天，减少 3 意志/天。不同行动导致的意志变化可叠加计算。</li>
                <li>魅力（Charisma）：每 1 点其它五种基础属性提升，增加 0.1 魅力。</li>
                <li>幻构师经验：初始值 1460；每绘画 1 小时，增长 10 经验。</li>
            </ul>
            <h3>幻构师计划（绘画技能体系）</h3>
            <ul>
                <li>Lv.1 描形学徒（总需求 1500）：初级（1-450）、中级（451-1050）、高级（1051-1500）</li>
                <li>Lv.2 构素学者（总需求 3000）：初级（1501-2400）、中级（2401-3600）、高级（3601-4500）</li>
                <li>Lv.3 灵绘使徒（总需求 5000）：初级（4501-6000）、中级（6001-8000）、高级（8001-9500）</li>
                <li>Lv.4 影纹术士（总需求 8000）：初级（9501-11900）、中级（11901-15100）、高级（15101-17500）</li>
                <li>Lv.5 心象织者（总需求 12000）：初级（17501-21100）、中级（21101-25900）、高级（25901-29500）</li>
                <li>Lv.6 空境画匠（总需求 18000）：初级（29501-34900）、中级（34901-42100）、高级（42101-47500）</li>
                <li>Lv.7 律令绘爵（总需求 26000）：初级（47501-55300）、中级（55301-65700）、高级（65701-73500）</li>
                <li>Lv.8 幻构师（总需求 36000）：初级（73501-84300）、中级（84301-98700）、高级（98701-109500）</li>
            </ul>
            <h3>真理之路计划</h3>
            <h4>知识侧</h4>
            <ul>
                <li>Lv.1 灰袍学徒（总需求 150）：初级（1-30）、中级（31-75）、高级（76-150）</li>
                <li>Lv.2 白袍向导（总需求 500）：初级（151-250）、中级（251-400）、高级（401-650）</li>
                <li>Lv.3 墨衣学者（总需求 1500）：初级（651-950）、中级（951-1400）、高级（1401-2150）</li>
                <li>Lv.4 青衿贤者（总需求 4000）：初级（2151-2950）、中级（2951-4150）、高级（4151-6150）</li>
                <li>Lv.5 玄冕宗师（总需求 10000）：初级（6151-8150）、中级（8151-11150）、高级（11151-16150）</li>
            </ul>
            <h4>智力侧</h4>
            <ul>
                <li>Lv.1 褐衣明理（总需求 150）：初级（1-30）、中级（31-75）、高级（76-150）</li>
                <li>Lv.2 缁衣慎思（总需求 500）：初级（151-250）、中级（251-400）、高级（401-650）</li>
                <li>Lv.3 朱衣审辩（总需求 1500）：初级（651-950）、中级（951-1400）、高级（1401-2150）</li>
                <li>Lv.4 紫绶格物（总需求 4000）：初级（2151-2950）、中级（2951-4150）、高级（4151-6150）</li>
                <li>Lv.5 金章弘道（总需求 10000）：初级（6151-8150）、中级（8151-11150）、高级（11151-16150）</li>
            </ul>
            <h3>晨曦之约计划</h3>
            <ul>
                <li>惩罚：每次未能按时睡觉减少 1 意志；每次未能按时起床减少 0.5 体力。</li>
                <li>奖励：持续 3 天以上：每天 +0.5 意志，+0.2 体力；持续 7 天以上：每天 +1 意志，+0.5 体力；持续 30 天以上：每天 +2 意志，+1 体力；特殊选项“早睡/早起”额外 +1 意志 / +0.5 体力。</li>
                <li>称号：</li>
                <ul>
                    <li>Lv.1 星辉学徒（连续坚持 7 天）：智力提升效率 +5%</li>
                    <li>Lv.2 晨风哨卫（连续坚持 30 天）：知识 + 智力提升效率 +5%</li>
                    <li>Lv.3 夜穹守誓（连续坚持 60 天）：全体属性提升效率 +5%</li>
                    <li>Lv.4 破晓骑士（连续坚持 90 天）：全体属性提升效率 +10%</li>
                    <li>Lv.5 黎明星使（连续坚持 120 天）：全体属性提升效率 +15%</li>
                    <li>Lv.6 永夜圣者（连续坚持 180 天）：全体属性提升效率 +20%</li>
                    <li>Lv.7 晨曦领主（连续坚持 365 天）：全体属性提升效率 +25%</li>
                    <li>Lv.8 时序主宰（连续坚持 730 天）：全体属性提升效率 +30%</li>
                </ul>
                <li>失约惩罚：获得 Lv.1 后，累计失败 1 天失去；Lv.2 累计失败 3 天失去；Lv.3 累计失败 6 天失去；Lv.4 累计失败 9 天失去；Lv.5 累计失败 12 天失去；Lv.6 累计失败 18 天失去；Lv.7 累计失败 36 天失去；Lv.8 累计失败 73 天失去。</li>
            </ul>
            <h3>称号系统</h3>
            <h4>阅识称号</h4>
            <ul>
                <li>Lv.1 历尘星火：所需阅识 50，奖励：智力提升效率 +5%</li>
                <li>Lv.2 历溪观澜：所需阅识 200，奖励：知识 + 智力提升效率 +5%</li>
                <li>Lv.3 历卷拓荒：所需阅识 500，奖励：全体属性提升效率 +5%</li>
                <li>Lv.4 历镜寻真：所需阅识 800，奖励：全体属性提升效率 +10%</li>
                <li>Lv.5 历川归海：所需阅识 1200，奖励：全体属性提升效率 +15%</li>
                <li>Lv.6 历世洞明：所需阅识 2000，奖励：全体属性提升效率 +20%</li>
                <li>Lv.7 历界织识：所需阅识 3000，奖励：全体属性提升效率 +25%</li>
                <li>Lv.8 历象归藏：所需阅识 5000，奖励：全体属性提升效率 +30%</li>
            </ul>
            <h4>意志称号</h4>
            <ul>
                <li>Lv.1 晨曦微志：所需意志 50，奖励：魅力增长效率 +5%</li>
                <li>Lv.2 坚石守心：所需意志 200，奖励：魅力增长效率 +10%</li>
                <li>Lv.3 荆棘先锋：所需意志 500，奖励：魅力增长效率 +15%</li>
                <li>Lv.4 钢铁铸意：所需意志 800，奖励：魅力增长效率 +20%</li>
                <li>Lv.5 风暴不屈：所需意志 1200，奖励：魅力增长效率 +25%</li>
                <li>Lv.6 星辰恒志：所需意志 2000，奖励：魅力增长效率 +30%</li>
                <li>Lv.7 炽魂永燃：所需意志 3000，奖励：魅力增长效率 +40%</li>
                <li>Lv.8 无朽之心：所需意志 5000，奖励：魅力增长效率 +50%</li>
            </ul>
            <h4>魅力称号</h4>
            <ul>
                <li>Lv.1 萤火微光：所需魅力 10，奖励：全体属性提升效率 +5%</li>
                <li>Lv.2 晨露流辉：所需魅力 50，奖励：全体属性提升效率 +10%</li>
                <li>Lv.3 星芒初绽：所需魅力 100，奖励：全体属性提升效率 +15%</li>
                <li>Lv.4 银月颂光：所需魅力 200，奖励：全体属性提升效率 +20%</li>
                <li>Lv.5 日冕凝华：所需魅力 300，奖励：全体属性提升效率 +25%</li>
                <li>Lv.6 虹彩冠冕：所需魅力 500，奖励：全体属性提升效率 +30%</li>
                <li>Lv.7 天穹律光：所需魅力 800，奖励：全体属性提升效率 +40%</li>
                <li>Lv.8 万象圣辉：所需魅力 1200，奖励：全体属性提升效率 +50%</li>
            </ul>
        </div>
    </div>

    <!-- JavaScript 部分 -->
    <script>
        /********************
         * 全局数据结构与初始化 *
         ********************/
        // 计划开始日期（可修改）
        let planStartDate = new Date(); // 默认今天，可通过导入数据时覆盖
        // 数据存储结构
        let data = {
            startDate: planStartDate.toISOString().substr(0, 10), // yyyy-mm-dd
            attributes: {
                intelligence: 0,
                knowledge: 0,
                readExp: 0,
                stamina: 0,
                will: 0,
                charisma: 0,
                artExp: 1460
            },
            // 连续打卡天数与失败天数计数
            dawn: {
                consecutiveSuccess: 0,
                consecutiveFail: 0,
                history: [] // { date: 'yyyy-mm-dd', success: true/false, earlySleep: bool, earlyRise: bool }
            },
            dailyRecords: [], // { date, artMin, studyMin, specialReadPages, exerciseMin, bookReadMin, videoWatchMin, chkWill flags, deltas: { artExpDelta, intDelta, knowledgeDelta, staminaDelta, willDelta, charismaDelta }, titleBonus, consecutiveStats }
            summaries: [], // { date, content }
            overallRecords: [], // { date, artProgress: { current, delta }, truthKnowledge: { current, delta }, truthIntelligence: { current, delta }, dawn: success/false, summary: content, tasksOverview: string }
            tasks: [], // { id, name, description, type, cycle, targetType, targetValue, deadline, penalties: [{ type, value }], rewards: [{ type, value }], createdDate, status: 'pending'/'completed', progressValue, completionDate }
        };

        // 阶段与称号信息定义（常量）
        const ART_LEVELS = [
            { level: 'Lv.1 描形学徒', totalReq: 1500, stages: [{ name: '初级', min: 1, max: 450 }, { name: '中级', min: 451, max: 1050 }, { name: '高级', min: 1051, max: 1500 }] },
            { level: 'Lv.2 构素学者', totalReq: 3000, stages: [{ name: '初级', min: 1501, max: 2400 }, { name: '中级', min: 2401, max: 3600 }, { name: '高级', min: 3601, max: 4500 }] },
            { level: 'Lv.3 灵绘使徒', totalReq: 5000, stages: [{ name: '初级', min: 4501, max: 6000 }, { name: '中级', min: 6001, max: 8000 }, { name: '高级', min: 8001, max: 9500 }] },
            { level: 'Lv.4 影纹术士', totalReq: 8000, stages: [{ name: '初级', min: 9501, max: 11900 }, { name: '中级', min: 11901, max: 15100 }, { name: '高级', min: 15101, max: 17500 }] },
            { level: 'Lv.5 心象织者', totalReq: 12000, stages: [{ name: '初级', min: 17501, max: 21100 }, { name: '中级', min: 21101, max: 25900 }, { name: '高级', min: 25901, max: 29500 }] },
            { level: 'Lv.6 空境画匠', totalReq: 18000, stages: [{ name: '初级', min: 29501, max: 34900 }, { name: '中级', min: 34901, max: 42100 }, { name: '高级', min: 42101, max: 47500 }] },
            { level: 'Lv.7 律令绘爵', totalReq: 26000, stages: [{ name: '初级', min: 47501, max: 55300 }, { name: '中级', min: 55301, max: 65700 }, { name: '高级', min: 65701, max: 73500 }] },
            { level: 'Lv.8 幻构师', totalReq: 36000, stages: [{ name: '初级', min: 73501, max: 84300 }, { name: '中级', min: 84301, max: 98700 }, { name: '高级', min: 98701, max: 109500 }] }
        ];
        const TRUTH_KNOWLEDGE_LEVELS = [
            { level: 'LV.1 灰袍学徒', totalReq: 150, stages: [{ name: '初级', min: 1, max: 30 }, { name: '中级', min: 31, max: 75 }, { name: '高级', min: 76, max: 150 }] },
            { level: 'LV.2 白袍向导', totalReq: 500, stages: [{ name: '初级', min: 151, max: 250 }, { name: '中级', min: 251, max: 400 }, { name: '高级', min: 401, max: 650 }] },
            { level: 'LV.3 墨衣学者', totalReq: 1500, stages: [{ name: '初级', min: 651, max: 950 }, { name: '中级', min: 951, max: 1400 }, { name: '高级', min: 1401, max: 2150 }] },
            { level: 'LV.4 青衿贤者', totalReq: 4000, stages: [{ name: '初级', min: 2151, max: 2950 }, { name: '中级', min: 2951, max: 4150 }, { name: '高级', min: 4151, max: 6150 }] },
            { level: 'LV.5 玄冕宗师', totalReq: 10000, stages: [{ name: '初级', min: 6151, max: 8150 }, { name: '中级', min: 8151, max: 11150 }, { name: '高级', min: 11151, max: 16150 }] }
        ];
        const TRUTH_INTELLIGENCE_LEVELS = [
            { level: 'LV.1 褐衣明理', totalReq: 150, stages: [{ name: '初级', min: 1, max: 30 }, { name: '中级', min: 31, max: 75 }, { name: '高级', min: 76, max: 150 }] },
            { level: 'LV.2 缁衣慎思', totalReq: 500, stages: [{ name: '初级', min: 151, max: 250 }, { name: '中级', min: 251, max: 400 }, { name: '高级', min: 401, max: 650 }] },
            { level: 'LV.3 朱衣审辩', totalReq: 1500, stages: [{ name: '初级', min: 651, max: 950 }, { name: '中级', min: 951, max: 1400 }, { name: '高级', min: 1401, max: 2150 }] },
            { level: 'LV.4 紫绶格物', totalReq: 4000, stages: [{ name: '初级', min: 2151, max: 2950 }, { name: '中级', min: 2951, max: 4150 }, { name: '高级', min: 4151, max: 6150 }] },
            { level: 'LV.5 金章弘道', totalReq: 10000, stages: [{ name: '初级', min: 6151, max: 8150 }, { name: '中级', min: 8151, max: 11150 }, { name: '高级', min: 11151, max: 16150 }] }
        ];
        const TITLES_DAWN = [
            { level: 1, name: '星辉学徒', reqDays: 7, reward: '智力提升效率 +5%' },
            { level: 2, name: '晨风哨卫', reqDays: 30, reward: '知识+智力提升效率 +5%' },
            { level: 3, name: '夜穹守誓', reqDays: 60, reward: '全体属性提升效率 +5%' },
            { level: 4, name: '破晓骑士', reqDays: 90, reward: '全体属性提升效率 +10%' },
            { level: 5, name: '黎明星使', reqDays: 120, reward: '全体属性提升效率 +15%' },
            { level: 6, name: '永夜圣者', reqDays: 180, reward: '全体属性提升效率 +20%' },
            { level: 7, name: '晨曦领主', reqDays: 365, reward: '全体属性提升效率 +25%' },
            { level: 8, name: '时序主宰', reqDays: 730, reward: '全体属性提升效率 +30%' }
        ];
        const TITLES_READ_EXP = [
            { level: 1, name: '历尘星火', req: 50, reward: '智力提升效率 +5%' },
            { level: 2, name: '历溪观澜', req: 200, reward: '知识+智力提升效率 +5%' },
            { level: 3, name: '历卷拓荒', req: 500, reward: '全体属性提升效率 +5%' },
            { level: 4, name: '历镜寻真', req: 800, reward: '全体属性提升效率 +10%' },
            { level: 5, name: '历川归海', req: 1200, reward: '全体属性提升效率 +15%' },
            { level: 6, name: '历世洞明', req: 2000, reward: '全体属性提升效率 +20%' },
            { level: 7, name: '历界织识', req: 3000, reward: '全体属性提升效率 +25%' },
            { level: 8, name: '历象归藏', req: 5000, reward: '全体属性提升效率 +30%' }
        ];
        const TITLES_WILL = [
            { level: 1, name: '晨曦微志', req: 50, reward: '魅力增长效率 +5%' },
            { level: 2, name: '坚石守心', req: 200, reward: '魅力增长效率 +10%' },
            { level: 3, name: '荆棘先锋', req: 500, reward: '魅力增长效率 +15%' },
            { level: 4, name: '钢铁铸意', req: 800, reward: '魅力增长效率 +20%' },
            { level: 5, name: '风暴不屈', req: 1200, reward: '魅力增长效率 +25%' },
            { level: 6, name: '星辰恒志', req: 2000, reward: '魅力增长效率 +30%' },
            { level: 7, name: '炽魂永燃', req: 3000, reward: '魅力增长效率 +40%' },
            { level: 8, name: '无朽之心', req: 5000, reward: '魅力增长效率 +50%' }
        ];
        const TITLES_CHARISMA = [
            { level: 1, name: '萤火微光', req: 10, reward: '全体属性提升效率 +5%' },
            { level: 2, name: '晨露流辉', req: 50, reward: '全体属性提升效率 +10%' },
            { level: 3, name: '星芒初绽', req: 100, reward: '全体属性提升效率 +15%' },
            { level: 4, name: '银月颂光', req: 200, reward: '全体属性提升效率 +20%' },
            { level: 5, name: '日冕凝华', req: 300, reward: '全体属性提升效率 +25%' },
            { level: 6, name: '虹彩冠冕', req: 500, reward: '全体属性提升效率 +30%' },
            { level: 7, name: '天穹律光', req: 800, reward: '全体属性提升效率 +40%' },
            { level: 8, name: '万象圣辉', req: 1200, reward: '全体属性提升效率 +50%' }
        ];

        // 当前标签页激活状态
        let currentTab = 'dailyRecordTab';

        // 分页状态
        const PAGE_SIZE = 10;
        let dailyHistoryPage = 1;
        let dawnHistoryPage = 1;
        let summaryHistoryPage = 1;
        let overallHistoryPage = 1;

        /*************************
         * 工具函数：日期与时间处理 *
         *************************/
        function formatDate(date) {
            let y = date.getFullYear();
            let m = String(date.getMonth() + 1).padStart(2, '0');
            let d = String(date.getDate()).padStart(2, '0');
            return `${y}-${m}-${d}`;
        }
        function parseDate(str) {
            let parts = str.split('-');
            return new Date(parts[0], parts[1] - 1, parts[2]);
        }
        // 计算两个日期之间相差的天数（包含开始日）
        function daysBetween(start, end) {
            let msPerDay = 24 * 60 * 60 * 1000;
            let st = Date.UTC(start.getFullYear(), start.getMonth(), start.getDate());
            let en = Date.UTC(end.getFullYear(), end.getMonth(), end.getDate());
            return Math.floor((en - st) / msPerDay) + 1;
        }
        // 获取北京时间当前时间字符串
        function getBeijingTimeString() {
            let now = new Date();
            // 将时间转换为北京时间（UTC+8）
            let utc = now.getTime() + now.getTimezoneOffset() * 60000;
            let beijing = new Date(utc + 3600000 * 8);
            let y = beijing.getFullYear();
            let m = String(beijing.getMonth() + 1).padStart(2, '0');
            let d = String(beijing.getDate()).padStart(2, '0');
            let hh = String(beijing.getHours()).padStart(2, '0');
            let mm = String(beijing.getMinutes()).padStart(2, '0');
            let ss = String(beijing.getSeconds()).padStart(2, '0');
            return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
        }
        // 更新头部当前时间与计划天数显示
        function updateHeaderTime() {
            document.getElementById('currentDatetime').innerText = getBeijingTimeString();
            let today = new Date();
            let start = parseDate(data.startDate);
            let days = daysBetween(start, today);
            document.getElementById('planDays').innerText = `计划启动天数：${days}`;
        }
        setInterval(updateHeaderTime, 1000);
        updateHeaderTime();

        /********************************
         * 导入/导出/保存/重置功能 *
         ********************************/
        // 保存数据到 localStorage
        function saveData() {
            localStorage.setItem('lifeGameData', JSON.stringify(data));
            alert('数据已保存');
        }
        // 从 localStorage 读取数据
        function loadData() {
            let stored = localStorage.getItem('lifeGameData');
            if (stored) {
                data = JSON.parse(stored);
                planStartDate = parseDate(data.startDate);
            }
        }
        // 导出数据为 JSON 文件
        function exportData() {
            let blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            let url = URL.createObjectURL(blob);
            let a = document.createElement('a');
            a.href = url;
            a.download = 'lifeGameData.json';
            a.click();
            URL.revokeObjectURL(url);
        }
        // 导入数据，从 JSON 文件加载
        function importData(file) {
            let reader = new FileReader();
            reader.onload = function(e) {
                try {
                    let imported = JSON.parse(e.target.result);
                    data = imported;
                    planStartDate = parseDate(data.startDate);
                    renderAll();
                    alert('数据导入成功');
                } catch (err) {
                    alert('导入数据格式错误');
                }
            };
            reader.readAsText(file);
        }
        // 重置所有数据
        function resetData() {
            if (!confirm('确认重置？所有数据将清空，且无法恢复。')) return;
            localStorage.removeItem('lifeGameData');
            // 重置为初始状态
            data = {
                startDate: formatDate(new Date()),
                attributes: {
                    intelligence: 0,
                    knowledge: 0,
                    readExp: 0,
                    stamina: 0,
                    will: 0,
                    charisma: 0,
                    artExp: 1460
                },
                dawn: {
                    consecutiveSuccess: 0,
                    consecutiveFail: 0,
                    history: []
                },
                dailyRecords: [],
                summaries: [],
                overallRecords: [],
                tasks: []
            };
            planStartDate = new Date();
            renderAll();
        }
        document.getElementById('saveBtn').addEventListener('click', saveData);
        document.getElementById('exportBtn').addEventListener('click', exportData);
        document.getElementById('importBtn').addEventListener('click', () => {
            let fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = 'application/json';
            fileInput.onchange = e => {
                let file = e.target.files[0];
                if (file) importData(file);
            };
            fileInput.click();
        });
        document.getElementById('resetBtn').addEventListener('click', resetData);

        /*****************************
         * 渲染“当前状态”区域函数 *
         *****************************/
        function calculateAttributes() {
            // 根据 dailyRecords 和 dawn.history 等计算累计属性值
            let attrs = {
                intelligence: 0,
                knowledge: 0,
                readExp: 0,
                stamina: 0,
                will: 0,
                charisma: 0,
                artExp: 1460
            };
            // 清除之前记录的各种连续天数状态
            let consecutive = {
                art: 0,
                study: 0,
                exercise: 0,
                book: 0,
                video: 0
            };
            let failCounts = {
                art: 0,
                study: 0,
                exercise: 0,
                book: 0,
                video: 0
            };
            let lastDateStr = null;
            data.dailyRecords.forEach(rec => {
                let dateStr = rec.date;
                // 幻构师经验
                attrs.artExp += rec.deltas.artExpDelta;
                // 基础属性
                attrs.intelligence += rec.deltas.intDelta;
                attrs.knowledge += rec.deltas.knowledgeDelta;
                attrs.stamina += rec.deltas.staminaDelta;
                attrs.will += rec.deltas.willDelta;
                attrs.readExp += rec.deltas.readExpDelta;
                // 魅力 = 0.1 * (其它五种属性总增量)
                // 但用户期望为累积式？此处我们直接用当前累计的其它属性总和 *0.1
                attrs.charisma = (attrs.intelligence + attrs.knowledge + attrs.readExp + attrs.stamina + attrs.will) * 0.1;
            });
            // 枚举“晨曦之约打卡”带来的奖励/惩罚对体力和意志的影响
            data.dawn.history.forEach(rec => {
                if (rec.success) {
                    // 计算连续天数并根据阈值给予奖励
                    // 首次或者前一天失败，重置连续计数
                    // 但 data.dawn.consecutiveSuccess 已在打卡时更新，这里直接合并 dailyRecords 中的 willDelta、staminaDelta 即可
                    // 所以无需重复计入
                } else {
                    // 同上，惩罚已在 dailyRecords 的 deltas 里体现
                }
            });
            // 任务系统中某些任务可能影响属性，假设在任务完成时已经直接修改 attributes
            // 最终返回计算后属性值
            return attrs;
        }
        function calculateCareerLevels() {
            // 幻构师等级
            let artExp = data.attributes.artExp;
            let artLevelStr = 'Lv.1 描形学徒（初级）';
            for (let lvlInfo of ART_LEVELS) {
                if (artExp <= lvlInfo.totalReq) {
                    // 找到所在等级
                    let stageName = '初级';
                    for (let st of lvlInfo.stages) {
                        if (artExp >= st.min && artExp <= st.max) {
                            stageName = st.name;
                            break;
                        }
                    }
                    artLevelStr = `${lvlInfo.level}（${stageName}）`;
                    break;
                }
            }
            // 若超过最后一级总需求，则视为 Lv.8 幻构师（高级）
            if (artExp > ART_LEVELS[ART_LEVELS.length - 1].totalReq) {
                artLevelStr = `Lv.8 幻构师（高级）`;
            }
            // 真理之路知识侧
            let knowledge = data.attributes.knowledge;
            let truthKnStr = 'LV.1 灰袍学徒（初级）';
            for (let lvlInfo of TRUTH_KNOWLEDGE_LEVELS) {
                if (knowledge <= lvlInfo.totalReq) {
                    let stageName = '初级';
                    for (let st of lvlInfo.stages) {
                        if (knowledge >= st.min && knowledge <= st.max) {
                            stageName = st.name;
                            break;
                        }
                    }
                    truthKnStr = `${lvlInfo.level}（${stageName}）`;
                    break;
                }
            }
            if (knowledge > TRUTH_KNOWLEDGE_LEVELS[TRUTH_KNOWLEDGE_LEVELS.length - 1].totalReq) {
                truthKnStr = `LV.5 玄冕宗师（高级）`;
            }
            // 真理之路智力侧
            let intelligence = data.attributes.intelligence;
            let truthIntStr = 'LV.1 褐衣明理（初级）';
            for (let lvlInfo of TRUTH_INTELLIGENCE_LEVELS) {
                if (intelligence <= lvlInfo.totalReq) {
                    let stageName = '初级';
                    for (let st of lvlInfo.stages) {
                        if (intelligence >= st.min && intelligence <= st.max) {
                            stageName = st.name;
                            break;
                        }
                    }
                    truthIntStr = `${lvlInfo.level}（${stageName}）`;
                    break;
                }
            }
            if (intelligence > TRUTH_INTELLIGENCE_LEVELS[TRUTH_INTELLIGENCE_LEVELS.length - 1].totalReq) {
                truthIntStr = `LV.5 金章弘道（高级）`;
            }
            return { artLevelStr, truthKnStr, truthIntStr };
        }
        function calculateTitles() {
            // 计算各称号
            let titles = {
                dawn: '无',
                will: '无',
                charisma: '无',
                readExp: '无'
            };
            // 晨曦之约称号：以 data.dawn.consecutiveSuccess 计算
            let cs = data.dawn.consecutiveSuccess;
            for (let i = TITLES_DAWN.length - 1; i >= 0; i--) {
                if (cs >= TITLES_DAWN[i].reqDays) {
                    titles.dawn = `${TITLES_DAWN[i].name}`;
                    break;
                }
            }
            // 意志称号
            let will = data.attributes.will;
            for (let i = TITLES_WILL.length - 1; i >= 0; i--) {
                if (will >= TITLES_WILL[i].req) {
                    titles.will = `${TITLES_WILL[i].name}`;
                    break;
                }
            }
            // 魅力称号
            let charisma = data.attributes.charisma;
            for (let i = TITLES_CHARISMA.length - 1; i >= 0; i--) {
                if (charisma >= TITLES_CHARISMA[i].req) {
                    titles.charisma = `${TITLES_CHARISMA[i].name}`;
                    break;
                }
            }
            // 阅识称号
            let readExp = data.attributes.readExp;
            for (let i = TITLES_READ_EXP.length - 1; i >= 0; i--) {
                if (readExp >= TITLES_READ_EXP[i].req) {
                    titles.readExp = `${TITLES_READ_EXP[i].name}`;
                    break;
                }
            }
            return titles;
        }
        function renderStatus() {
            // 先计算属性、职业等级、称号，再渲染
            let attrs = calculateAttributes();
            data.attributes = attrs; // 更新全局属性
            document.getElementById('attrInt').innerText = attrs.intelligence.toFixed(2);
            document.getElementById('attrKnowledge').innerText = attrs.knowledge.toFixed(2);
            document.getElementById('attrReadExp').innerText = attrs.readExp.toFixed(2);
            document.getElementById('attrStamina').innerText = attrs.stamina.toFixed(2);
            document.getElementById('attrWill').innerText = attrs.will.toFixed(2);
            document.getElementById('attrCharisma').innerText = attrs.charisma.toFixed(2);
            document.getElementById('attrArtExp').innerText = attrs.artExp.toFixed(0);

            let career = calculateCareerLevels();
            document.getElementById('careerArtLevel').innerText = career.artLevelStr;
            document.getElementById('careerTruthKnowledge').innerText = career.truthKnStr;
            document.getElementById('careerTruthIntelligence').innerText = career.truthIntStr;

            let titles = calculateTitles();
            document.getElementById('titleDawn').innerText = titles.dawn;
            document.getElementById('titleWill').innerText = titles.will;
            document.getElementById('titleCharisma').innerText = titles.charisma;
            document.getElementById('titleReadExp').innerText = titles.readExp;
        }

        /******************************
         * 选项卡切换逻辑 *
         ******************************/
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                if (tab.classList.contains('active')) return;
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(tc => tc.classList.remove('active'));
                tab.classList.add('active');
                document.getElementById(tab.dataset.tab).classList.add('active');
                currentTab = tab.dataset.tab;
            });
        });

        /***********************************
         * “每日记录” 功能实现 *
         ***********************************/
        function addDailyRecord() {
            let today = formatDate(new Date());
            // 防止重复添加同一天记录
            if (data.dailyRecords.some(r => r.date === today)) {
                alert('今日记录已存在，若需修改请编辑历史记录');
                return;
            }
            let artMin = parseInt(document.getElementById('inputArtMinutes').value) || 0;
            let studyMin = parseInt(document.getElementById('inputStudyMinutes').value) || 0;
            let specialReadPages = parseInt(document.getElementById('inputSpecialReadPages').value) || 0;
            let exerciseMin = parseInt(document.getElementById('inputExerciseMinutes').value) || 0;
            let bookReadMin = parseInt(document.getElementById('inputBookReadMinutes').value) || 0;
            let videoWatchMin = parseInt(document.getElementById('inputVideoWatchMinutes').value) || 0;
            let chkWillArt = document.getElementById('chkWillArt').checked;
            let chkWillStudy = document.getElementById('chkWillStudy').checked;
            let chkWillExercise = document.getElementById('chkWillExercise').checked;
            let chkWillBook = document.getElementById('chkWillBook').checked;
            let chkWillVideo = document.getElementById('chkWillVideo').checked;
            // 计算增量
            // ArtExp: 每分钟绘画经验增量 = 10/60
            let artExpDelta = artMin * (10 / 60);
            // 智力：学习1小时（60分钟）+1智力
            let intDelta = (studyMin
            // 阅识：阅读书籍1小时(60分钟)=1阅识，观看影视1小时(60分钟)=1阅识
            let readExpDelta = (bookReadMin / 60) * 1 + (videoWatchMin / 60) * 1;
            // 体力：运动1小时(60分钟)=1体力
            let staminaDelta = (exerciseMin / 60) * 1;
            // 意志增量：根据连续执行情况
            // 需要先判断各行为是否连续执行
            // 获取昨天记录
            let yesterday = formatDate(new Date(Date.now() - 86400000));
            // 查找昨日记录
            let yRec = data.dailyRecords.find(r => r.date === yesterday);
            // art 连续执行？
            let consecutive = { art: 0, study: 0, exercise: 0, book: 0, video: 0 };
            let willDelta = 0;
            // 若昨日存在并且今日行为 >0，则连续+1，否则为1
            if (artMin > 0) consecutive.art = (yRec && yRec.artMin > 0) ? yRec.consecutive.art + 1 : 1;
            else consecutive.art = 0;
            if (studyMin > 0) consecutive.study = (yRec && yRec.studyMin > 0) ? yRec.consecutive.study + 1 : 1;
            else consecutive.study = 0;
            if (exerciseMin > 0) consecutive.exercise = (yRec && yRec.exerciseMin > 0) ? yRec.consecutive.exercise + 1 : 1;
            else consecutive.exercise = 0;
            if (bookReadMin > 0) consecutive.book = (yRec && yRec.bookReadMin > 0) ? yRec.consecutive.book + 1 : 1;
            else consecutive.book = 0;
            if (videoWatchMin > 0) consecutive.video = (yRec && yRec.videoWatchMin > 0) ? yRec.consecutive.video + 1 : 1;
            else consecutive.video = 0;
            // 对每项行为计算意志增量
            function calcWillForAction(actionConsecutive, chkFlag) {
                if (!chkFlag) return 0;
                if (actionConsecutive >= 30) return 3;
                if (actionConsecutive >= 7) return 2;
                if (actionConsecutive >= 3) return 1;
                return 0;
            }
            willDelta += calcWillForAction(consecutive.art, chkWillArt);
            willDelta += calcWillForAction(consecutive.study, chkWillStudy);
            willDelta += calcWillForAction(consecutive.exercise, chkWillExercise);
            willDelta += calcWillForAction(consecutive.book, chkWillBook);
            willDelta += calcWillForAction(consecutive.video, chkWillVideo);
            // 若某行为今日=0且昨日>0，视为失败1天
            function calcWillLoss(yesterdayMin, todayMin) {
                if (yesterdayMin > 0 && todayMin === 0) return 1;
                return 0;
            }
            let willLoss = 0;
            if (yRec) {
                willLoss += chkWillArt ? calcWillLoss(yRec.artMin, artMin) : 0;
                willLoss += chkWillStudy ? calcWillLoss(yRec.studyMin, studyMin) : 0;
                willLoss += chkWillExercise ? calcWillLoss(yRec.exerciseMin, exerciseMin) : 0;
                willLoss += chkWillBook ? calcWillLoss(yRec.bookReadMin, bookReadMin) : 0;
                willLoss += chkWillVideo ? calcWillLoss(yRec.videoWatchMin, videoWatchMin) : 0;
            }
            willDelta -= willLoss;
            // 魅力增量 = 0.1 * (intDelta+knowledgeDelta+readExpDelta+staminaDelta+willDelta)
            let charismaDelta = 0.1 * (intDelta + knowledgeDelta + readExpDelta + staminaDelta + willDelta);
            // 将今天的连续记录与昨日的关联用于明天计算
            let todaysConsecutive = { ...consecutive };
            // 计算称号奖励加成字符串（简化：仅标注当前已获得的称号，不具体计算效率叠加）
            let titleBonus = '';
            let titles = calculateTitles();
            titleBonus = `当前称号：晨曦[${titles.dawn}] 意志[${titles.will}] 魅力[${titles.charisma}] 阅识[${titles.readExp}]`;
            // 记录连续成功/失败时间：每个事件的连续成功天数，若该行为失败，则连续失败几天
            let consecutiveStatsStr = `绘画连续${consecutive.art}天/学习连续${consecutive.study}天/运动连续${consecutive.exercise}天/阅书连续${consecutive.book}天/阅影连续${consecutive.video}天`;
            // 添加记录
            data.dailyRecords.push({
                date: today,
                artMin, studyMin, specialReadPages, exerciseMin, bookReadMin, videoWatchMin,
                chkWillArt, chkWillStudy, chkWillExercise, chkWillBook, chkWillVideo,
                deltas: { artExpDelta, intDelta, knowledgeDelta, staminaDelta, willDelta, readExpDelta, charismaDelta },
                titleBonus,
                consecutive: todaysConsecutive,
                consecutiveStatsStr
            });
            // 同时更新属性并记录至 overallRecords
            updateOverallRecordForDate(today);
            renderAll();
        }
        document.getElementById('addDailyRecordBtn').addEventListener('click', addDailyRecord);

        // 渲染“每日历史记录”表格
        function renderDailyHistory() {
            let tbody = document.getElementById('dailyHistoryBody');
            tbody.innerHTML = '';
            let records = data.dailyRecords.slice().sort((a, b) => b.date.localeCompare(a.date));
            let totalPages = Math.ceil(records.length / PAGE_SIZE);
            if (dailyHistoryPage > totalPages) dailyHistoryPage = totalPages || 1;
            let startIdx = (dailyHistoryPage - 1) * PAGE_SIZE;
            let pageRecords = records.slice(startIdx, startIdx + PAGE_SIZE);
            pageRecords.forEach(rec => {
                let tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${rec.date}</td>
                    <td>${rec.studyMin} 分钟</td>
                    <td>${rec.specialReadPages} 页</td>
                    <td>${rec.artMin} 分钟</td>
                    <td>${rec.bookReadMin} 分钟</td>
                    <td>${rec.videoWatchMin} 分钟</td>
                    <td>${rec.deltas.artExpDelta.toFixed(2)}</td>
                    <td>${rec.deltas.intDelta.toFixed(2)}</td>
                    <td>${rec.deltas.knowledgeDelta.toFixed(2)}</td>
                    <td>${rec.deltas.staminaDelta.toFixed(2)}</td>
                    <td>${rec.deltas.willDelta.toFixed(2)}</td>
                    <td>${rec.deltas.charismaDelta.toFixed(2)}</td>
                    <td>${rec.titleBonus}</td>
                    <td>${rec.consecutiveStatsStr}</td>
                    <td>
                        <button class="editDailyBtn" data-date="${rec.date}">编辑</button>
                        <button class="deleteDailyBtn" data-date="${rec.date}">删除</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
            // 分页按钮
            let pagination = document.getElementById('dailyHistoryPagination');
            pagination.innerHTML = '';
            for (let i = 1; i <= totalPages; i++) {
                let btn = document.createElement('button');
                btn.innerText = i;
                if (i === dailyHistoryPage) btn.classList.add('active');
                btn.addEventListener('click', () => {
                    dailyHistoryPage = i;
                    renderDailyHistory();
                });
                pagination.appendChild(btn);
            }
            // 绑定编辑与删除
            document.querySelectorAll('.editDailyBtn').forEach(btn => {
                btn.addEventListener('click', () => {
                    editDailyRecord(btn.dataset.date);
                });
            });
            document.querySelectorAll('.deleteDailyBtn').forEach(btn => {
                btn.addEventListener('click', () => {
                    deleteDailyRecord(btn.dataset.date);
                });
            });
        }
        // 编辑记录（仅示例，弹窗或表单实现较复杂，此处仅提示不允许编辑当天记录）
        function editDailyRecord(date) {
            alert(`请先删除当天记录后重新添加。编辑功能暂未实现。`);
        }
        function deleteDailyRecord(date) {
            if (!confirm(`确定删除 ${date} 的记录？`)) return;
            data.dailyRecords = data.dailyRecords.filter(r => r.date !== date);
            // 同时删除对应 overallRecords
            data.overallRecords = data.overallRecords.filter(r => r.date !== date);
            renderAll();
        }
        document.getElementById('toggleDailyHistoryBtn').addEventListener('click', () => {
            let container = document.getElementById('dailyHistoryContainer');
            container.style.display = container.style.display === 'none' ? 'block' : 'none';
            if (container.style.display === 'block') renderDailyHistory();
        });
        // 初始隐藏历史
        document.getElementById('dailyHistoryContainer').style.display = 'none';

        /***********************************
         * “晨曦之约计划” 功能实现 *
         ***********************************/
        function addDawnRecord() {
            let today = formatDate(new Date());
            if (data.dawn.history.some(r => r.date === today)) {
                alert('今日打卡记录已存在，若需修改请编辑历史记录');
                return;
            }
            let sleptOnTime = document.getElementById('chkSleptOnTime').checked;
            let wokeOnTime = document.getElementById('chkWokeOnTime').checked;
            let specialCase = document.getElementById('chkSpecialCase').checked;
            let earlySleep = document.getElementById('chkEarlySleep').checked;
            let earlyRise = document.getElementById('chkEarlyRise').checked;
            let success = false;
            if (specialCase) {
                success = true;
            } else {
                success = sleptOnTime && wokeOnTime;
            }
            // 更新连续成功/失败计数
            if (data.dawn.history.length > 0) {
                let yesterday = formatDate(new Date(Date.now() - 86400000));
                let yRec = data.dawn.history.find(r => r.date === yesterday);
                if (success) {
                    data.dawn.consecutiveSuccess = (yRec && yRec.success) ? yRec.consecutiveSuccess + 1 : 1;
                    data.dawn.consecutiveFail = 0;
                } else {
                    data.dawn.consecutiveFail = (yRec && !yRec.success) ? yRec.consecutiveFail + 1 : 1;
                    data.dawn.consecutiveSuccess = 0;
                }
            } else {
                if (success) {
                    data.dawn.consecutiveSuccess = 1;
                    data.dawn.consecutiveFail = 0;
                } else {
                    data.dawn.consecutiveFail = 1;
                    data.dawn.consecutiveSuccess = 0;
                }
            }
            // 计算意志/体力增减
            let willDelta = 0;
            let staminaDelta = 0;
            if (success) {
                let cs = data.dawn.consecutiveSuccess;
                if (cs >= 30) { willDelta += 2; staminaDelta += 1; }
                else if (cs >= 7) { willDelta += 1; staminaDelta += 0.5; }
                else if (cs >= 3) { willDelta += 0.5; staminaDelta += 0.2; }
                if (earlySleep) willDelta += 1;
                if (earlyRise) staminaDelta += 0.5;
            } else {
                // 失败惩罚
                // 每次未能按时睡觉（未勾选 sleptOnTime）减少 1 意志
                if (!sleptOnTime) willDelta -= 1;
                // 未能按时起床减少 0.5 体力
                if (!wokeOnTime) staminaDelta -= 0.5;
                // 连续失败≥3天，每天额外 -2 意志
                if (data.dawn.consecutiveFail >= 7) willDelta -= 3;
                else if (data.dawn.consecutiveFail >= 3) willDelta -= 2;
            }
            // 更新属性
            data.attributes.will += willDelta;
            data.attributes.stamina += staminaDelta;
            // 记录
            data.dawn.history.push({
                date: today,
                success,
                earlySleep,
                earlyRise,
                willDelta,
                staminaDelta,
                consecutiveSuccess: data.dawn.consecutiveSuccess,
                consecutiveFail: data.dawn.consecutiveFail
            });
            updateOverallRecordForDate(today);
            renderAll();
        }
        document.getElementById('addDawnRecordBtn').addEventListener('click', addDawnRecord);
        // 渲染“晨曦历史记录”
        function renderDawnHistory() {
            let tbody = document.getElementById('dawnHistoryBody');
            tbody.innerHTML = '';
            let records = data.dawn.history.slice().sort((a, b) => b.date.localeCompare(a.date));
            let totalPages = Math.ceil(records.length / PAGE_SIZE);
            if (dawnHistoryPage > totalPages) dawnHistoryPage = totalPages || 1;
            let startIdx = (dawnHistoryPage - 1) * PAGE_SIZE;
            let pageRecords = records.slice(startIdx, startIdx + PAGE_SIZE);
            pageRecords.forEach(rec => {
                let tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${rec.date}</td>
                    <td>${rec.success ? '成功' : '失败'}</td>
                    <td>${rec.earlySleep ? '是' : '否'}</td>
                    <td>${rec.earlyRise ? '是' : '否'}</td>
                    <td>${rec.willDelta}</td>
                    <td>${rec.staminaDelta}</td>
                    <td>
                        <button class="editDawnBtn" data-date="${rec.date}">编辑</button>
                        <button class="deleteDawnBtn" data-date="${rec.date}">删除</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
            // 分页
            let pagination = document.getElementById('dawnHistoryPagination');
            pagination.innerHTML = '';
            for (let i = 1; i <= totalPages; i++) {
                let btn = document.createElement('button');
                btn.innerText = i;
                if (i === dawnHistoryPage) btn.classList.add('active');
                btn.addEventListener('click', () => {
                    dawnHistoryPage = i;
                    renderDawnHistory();
                });
                pagination.appendChild(btn);
            }
            // 绑定编辑与删除
            document.querySelectorAll('.editDawnBtn').forEach(btn => {
                btn.addEventListener('click', () => {
                    alert('编辑功能暂未实现，删除后可重新添加');
                });
            });
            document.querySelectorAll('.deleteDawnBtn').forEach(btn => {
                btn.addEventListener('click', () => {
                    deleteDawnRecord(btn.dataset.date);
                });
            });
        }
        function deleteDawnRecord(date) {
            if (!confirm(`确定删除 ${date} 的晨曦之约记录？`)) return;
            data.dawn.history = data.dawn.history.filter(r => r.date !== date);
            // 重置连续计数并重算所有后续历史记录
            recomputeDawnConsecutive();
            // 更新 overallRecords
            data.overallRecords = data.overallRecords.filter(r => r.date !== date);
            renderAll();
        }
        function recomputeDawnConsecutive() {
            data.dawn.consecutiveSuccess = 0;
            data.dawn.consecutiveFail = 0;
            let sorted = data.dawn.history.slice().sort((a, b) => a.date.localeCompare(b.date));
            sorted.forEach(rec => {
                if (rec.success) {
                    data.dawn.consecutiveSuccess = data.dawn.consecutiveSuccess + 1;
                    rec.consecutiveSuccess = data.dawn.consecutiveSuccess;
                    data.dawn.consecutiveFail = 0;
                    rec.consecutiveFail = 0;
                } else {
                    data.dawn.consecutiveFail = data.dawn.consecutiveFail + 1;
                    rec.consecutiveFail = data.dawn.consecutiveFail;
                    data.dawn.consecutiveSuccess = 0;
                    rec.consecutiveSuccess = 0;
                }
            });
        }
        document.getElementById('toggleDawnHistoryBtn').addEventListener('click', () => {
            let container = document.getElementById('dawnHistoryContainer');
            container.style.display = container.style.display === 'none' ? 'block' : 'none';
            if (container.style.display === 'block') renderDawnHistory();
        });
        document.getElementById('dawnHistoryContainer').style.display = 'none';

        /*************************************
         * “每日个人总结” 功能实现 *
         *************************************/
        function addDailySummary() {
            let date = document.getElementById('inputSummaryDate').value;
            if (!date) {
                alert('请选择日期');
                return;
            }
            if (data.summaries.some(s => s.date === date)) {
                alert('该日期已有总结，若需修改请编辑历史记录');
                return;
            }
            let content = document.getElementById('inputSummaryContent').value;
            data.summaries.push({ date, content });
            updateOverallRecordForDate(date);
            renderAll();
        }
        document.getElementById('addDailySummaryBtn').addEventListener('click', addDailySummary);
        function renderSummaryHistory() {
            let tbody = document.getElementById('summaryHistoryBody');
            tbody.innerHTML = '';
            let recs = data.summaries.slice().sort((a, b) => b.date.localeCompare(a.date));
            let totalPages = Math.ceil(recs.length / PAGE_SIZE);
            if (summaryHistoryPage > totalPages) summaryHistoryPage = totalPages || 1;
            let startIdx = (summaryHistoryPage - 1) * PAGE_SIZE;
            let pageRecs = recs.slice(startIdx, startIdx + PAGE_SIZE);
            pageRecs.forEach(rec => {
                let tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${rec.date}</td>
                    <td>${rec.content}</td>
                    <td>
                        <button class="editSummaryBtn" data-date="${rec.date}">编辑</button>
                        <button class="deleteSummaryBtn" data-date="${rec.date}">删除</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
            // 分页
            let pagination = document.getElementById('summaryHistoryPagination');
            pagination.innerHTML = '';
            for (let i = 1; i <= totalPages; i++) {
                let btn = document.createElement('button');
                btn.innerText = i;
                if (i === summaryHistoryPage) btn.classList.add('active');
                btn.addEventListener('click', () => {
                    summaryHistoryPage = i;
                    renderSummaryHistory();
                });
                pagination.appendChild(btn);
            }
            document.querySelectorAll('.editSummaryBtn').forEach(btn => {
                btn.addEventListener('click', () => {
                    editSummary(btn.dataset.date);
                });
            });
            document.querySelectorAll('.deleteSummaryBtn').forEach(btn => {
                btn.addEventListener('click', () => {
                    deleteSummary(btn.dataset.date);
                });
            });
        }
        function editSummary(date) {
            let rec = data.summaries.find(s => s.date === date);
            if (!rec) return;
            let newContent = prompt('编辑总结内容：', rec.content);
            if (newContent !== null) {
                rec.content = newContent;
                updateOverallRecordForDate(date);
                renderAll();
            }
        }
        function deleteSummary(date) {
            if (!confirm(`确定删除 ${date} 的总结？`)) return;
            data.summaries = data.summaries.filter(s => s.date !== date);
            data.overallRecords = data.overallRecords.filter(r => r.date !== date);
            renderAll();
        }
        document.getElementById('toggleSummaryHistoryBtn').addEventListener('click', () => {
            let container = document.getElementById('summaryHistoryContainer');
            container.style.display = container.style.display === 'none' ? 'block' : 'none';
            if (container.style.display === 'block') renderSummaryHistory();
        });
        document.getElementById('summaryHistoryContainer').style.display = 'none';

        /**************************************
         * “每日总记录” 功能实现 *
         **************************************/
        function updateOverallRecordForDate(date) {
            // 查找 dailyRecords、dawn、summaries，并计算任务概览
            let dailyRec = data.dailyRecords.find(r => r.date === date);
            let dawnRec = data.dawn.history.find(r => r.date === date);
            let summaryRec = data.summaries.find(s => s.date === date);
            // 计算幻构师进度增减
            let artPrev = 0, artCurr = data.attributes.artExp;
            // 要找到前一天的 artExp 值
            let yesterday = formatDate(new Date(parseDate(date).getTime() - 86400000));
            let yDaily = data.dailyRecords.find(r => r.date === yesterday);
            if (yDaily) {
                artPrev = data.artExp ? artPrev : 0; // already included in data.attributes
                // Recompute by summing all previous deltas
                let sumExp = 1460;
                data.dailyRecords.filter(r => r.date < date).forEach(r => sumExp += r.deltas.artExpDelta);
                artPrev = sumExp;
            } else {
                // 若无昨日记录，假设昨日 artExp = 初始 artExp + 所有早于昨日记录的 deltas
                let sumExp = 1460;
                data.dailyRecords.filter(r => r.date < date).forEach(r => sumExp += r.deltas.artExpDelta);
                artPrev = sumExp;
            }
            let artDelta = artCurr - artPrev;
            // 真理之路知识侧：当前知识与昨日知识差
            let knPrev = 0, knCurr = data.attributes.knowledge;
            let sumKn = 0;
            data.dailyRecords.filter(r => r.date < date).forEach(r => sumKn += r.deltas.knowledgeDelta);
            knPrev = sumKn;
            let knDelta = knCurr - knPrev;
            // 真理之路智力侧
            let intPrev = 0, intCurr = data.attributes.intelligence;
            let sumInt = 0;
            data.dailyRecords.filter(r => r.date < date).forEach(r => sumInt += r.deltas.intDelta);
            intPrev = sumInt;
            let intDelta = intCurr - intPrev;
            // 任务概览（简单显示 pending tasks 数量）
            let pendingCount = data.tasks.filter(t => t.status === 'pending').length;
            let tasksOverview = `待完成任务：${pendingCount} 个`;
            // 查看已有记录
            let existing = data.overallRecords.find(r => r.date === date);
            if (existing) {
                existing.artProgress = { current: artCurr, delta: artDelta };
                existing.truthKnowledge = { current: knCurr, delta: knDelta };
                existing.truthIntelligence = { current: intCurr, delta: intDelta };
                existing.dawn = dawnRec ? (dawnRec.success ? '成功' : '失败') : '无';
                existing.summary = summaryRec ? summaryRec.content : '';
                existing.tasksOverview = tasksOverview;
            } else {
                data.overallRecords.push({
                    date,
                    artProgress: { current: artCurr, delta: artDelta },
                    truthKnowledge: { current: knCurr, delta: knDelta },
                    truthIntelligence: { current: intCurr, delta: intDelta },
                    dawn: dawnRec ? (dawnRec.success ? '成功' : '失败') : '无',
                    summary: summaryRec ? summaryRec.content : '',
                    tasksOverview
                });
            }
        }
        function renderOverallHistory() {
            let tbody = document.getElementById('overallHistoryBody');
            tbody.innerHTML = '';
            let recs = data.overallRecords.slice().sort((a, b) => b.date.localeCompare(a.date));
            let totalPages = Math.ceil(recs.length / PAGE_SIZE);
            if (overallHistoryPage > totalPages) overallHistoryPage = totalPages || 1;
            let startIdx = (overallHistoryPage - 1) * PAGE_SIZE;
            let pageRecs = recs.slice(startIdx, startIdx + PAGE_SIZE);
            pageRecs.forEach(rec => {
                let tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${rec.date}</td>
                    <td>${rec.artProgress.current.toFixed(2)} (${rec.artProgress.delta.toFixed(2)})</td>
                    <td>${rec.truthKnowledge.current.toFixed(2)} (${rec.truthKnowledge.delta.toFixed(2)})</td>
                    <td>${rec.truthIntelligence.current.toFixed(2)} (${rec.truthIntelligence.delta.toFixed(2)})</td>
                    <td>${rec.dawn}</td>
                    <td>${rec.summary}</td>
                    <td>${rec.tasksOverview}</td>
                    <td>
                        <button class="editOverallBtn" data-date="${rec.date}">编辑</button>
                        <button class="deleteOverallBtn" data-date="${rec.date}">删除</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
            // 分页
            let pagination = document.getElementById('overallHistoryPagination');
            pagination.innerHTML = '';
            for (let i = 1; i <= totalPages; i++) {
                let btn = document.createElement('button');
                btn.innerText = i;
                if (i === overallHistoryPage) btn.classList.add('active');
                btn.addEventListener('click', () => {
                    overallHistoryPage = i;
                    renderOverallHistory();
                });
                pagination.appendChild(btn);
            }
            document.querySelectorAll('.editOverallBtn').forEach(btn => {
                btn.addEventListener('click', () => {
                    alert('编辑功能暂未实现');
                });
            });
            document.querySelectorAll('.deleteOverallBtn').forEach(btn => {
                btn.addEventListener('click', () => {
                    deleteOverallRecord(btn.dataset.date);
                });
            });
        }
        function deleteOverallRecord(date) {
            if (!confirm(`确定删除 ${date} 的总记录？`)) return;
            data.overallRecords = data.overallRecords.filter(r => r.date !== date);
            // 同时删除 daily、dawn、summary 中该日期的数据
            data.dailyRecords = data.dailyRecords.filter(r => r.date !== date);
            data.dawn.history = data.dawn.history.filter(r => r.date !== date);
            data.summaries = data.summaries.filter(r => r.date !== date);
            renderAll();
        }
        document.getElementById('toggleOverallHistoryBtn').addEventListener('click', () => {
            let container = document.getElementById('overallHistoryContainer');
            container.style.display = container.style.display === 'none' ? 'block' : 'none';
            if (container.style.display === 'block') renderOverallHistory();
        });
        document.getElementById('overallHistoryContainer').style.display = 'none';

        /***********************************
         * “幻构师计划” 功能实现 *
         ***********************************/
        function renderArtPlan() {
            let tbody = document.getElementById('artPlanTable').querySelector('tbody');
            tbody.innerHTML = '';
            let currentExp = data.attributes.artExp;
            ART_LEVELS.forEach(lvlInfo => {
                let row = document.createElement('tr');
                let stageName = '未达成';
                if (currentExp > lvlInfo.totalReq) {
                    stageName = '已超出';
                } else {
                    for (let st of lvlInfo.stages) {
                        if (currentExp >= st.min && currentExp <= st.max) {
                            stageName = st.name;
                            break;
                        }
                    }
                }
                let progressPct = Math.min((currentExp / lvlInfo.totalReq) * 100, 100).toFixed(2);
                row.innerHTML = `
                    <td>${lvlInfo.level}</td>
                    <td>${lvlInfo.totalReq}</td>
                    <td>${stageName}</td>
                    <td>${Math.min(currentExp, lvlInfo.totalReq)}</td>
                    <td>
                        <div class="progress-container"><div class="progress-bar" style="width: ${progressPct}%;"></div></div>
                        ${progressPct}%
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        /*************************************
         * “真理之路计划” 功能实现 *
         *************************************/
        function renderTruthPlan() {
            // 知识侧
            let tbKn = document.getElementById('truthKnowledgeTable').querySelector('tbody');
            tbKn.innerHTML = '';
            let currentKn = data.attributes.knowledge;
            TRUTH_KNOWLEDGE_LEVELS.forEach(lvlInfo => {
                let row = document.createElement('tr');
                let stageName = '未达成';
                if (currentKn > lvlInfo.totalReq) {
                    stageName = '已超出';
                } else {
                    for (let st of lvlInfo.stages) {
                        if (currentKn >= st.min && currentKn <= st.max) {
                            stageName = st.name;
                            break;
                        }
                    }
                }
                let progressPct = Math.min((currentKn / lvlInfo.totalReq) * 100, 100).toFixed(2);
                row.innerHTML = `
                    <td>${lvlInfo.level}</td>
                    <td>${lvlInfo.totalReq}</td>
                    <td>${stageName}</td>
                    <td>${Math.min(currentKn, lvlInfo.totalReq)}</td>
                    <td>
                        <div class="progress-container"><div class="progress-bar" style="width: ${progressPct}%;"></div></div>
                        ${progressPct}%
                    </td>
                `;
                tbKn.appendChild(row);
            });
            // 智力侧
            let tbInt = document.getElementById('truthIntelligenceTable').querySelector('tbody');
            tbInt.innerHTML = '';
            let currentInt = data.attributes.intelligence;
            TRUTH_INTELLIGENCE_LEVELS.forEach(lvlInfo => {
                let row = document.createElement('tr');
                let stageName = '未达成';
                if (currentInt > lvlInfo.totalReq) {
                    stageName = '已超出';
                } else {
                    for (let st of lvlInfo.stages) {
                        if (currentInt >= st.min && currentInt <= st.max) {
                            stageName = st.name;
                            break;
                        }
                    }
                }
                let progressPct = Math.min((currentInt / lvlInfo.totalReq) * 100, 100).toFixed(2);
                row.innerHTML = `
                    <td>${lvlInfo.level}</td>
                    <td>${lvlInfo.totalReq}</td>
                    <td>${stageName}</td>
                    <td>${Math.min(currentInt, lvlInfo.totalReq)}</td>
                    <td>
                        <div class="progress-container"><div class="progress-bar" style="width: ${progressPct}%;"></div></div>
                        ${progressPct}%
                    </td>
                `;
                tbInt.appendChild(row);
            });
        }

        /******************************
         * “称号系统” 功能实现 *
         ******************************/
        function renderTitles() {
            // 晨曦之约称号
            let tbDawn = document.getElementById('titlesDawnTable').querySelector('tbody');
            tbDawn.innerHTML = '';
            let cs = data.dawn.consecutiveSuccess;
            TITLES_DAWN.forEach(title => {
                let row = document.createElement('tr');
                let attained = cs >= title.req;
                let progressPct = attained ? 100 : ((cs / title.req) * 100).toFixed(2);
                row.innerHTML = `
                    <td>${title.level}</td>
                    <td>${title.name}</td>
                    <td>${title.req}</td>
                    <td>${cs}</td>
                    <td>${title.reward}</td>
                    <td>
                        ${attained ? '<span style="color: green; font-weight: bold;">已获得</span>' : `
                        <div class="progress-container"><div class="progress-bar" style="width: ${progressPct}%;"></div></div>
                        ${progressPct}%`}
                    </td>
                `;
                tbDawn.appendChild(row);
            });
            // 意志称号
            let tbWill = document.getElementById('titlesWillTable').querySelector('tbody');
            tbWill.innerHTML = '';
            let will = data.attributes.will;
            TITLES_WILL.forEach(title => {
                let row = document.createElement('tr');
                let attained = will >= title.req;
                let progressPct = attained ? 100 : ((will / title.req) * 100).toFixed(2);
                row.innerHTML = `
                    <td>${title.level}</td>
                    <td>${title.name}</td>
                    <td>${title.req}</td>
                    <td>${will.toFixed(2)}</td>
                    <td>${title.reward}</td>
                    <td>
                        ${attained ? '<span style="color: green; font-weight: bold;">已获得</span>' : `
                        <div class="progress-container"><div class="progress-bar" style="width: ${progressPct}%;"></div></div>
                        ${progressPct}%`}
                    </td>
                `;
                tbWill.appendChild(row);
            });
            // 魅力称号
            let tbChar = document.getElementById('titlesCharismaTable').querySelector('tbody');
            tbChar.innerHTML = '';
            let cha = data.attributes.charisma;
            TITLES_CHARISMA.forEach(title => {
                let row = document.createElement('tr');
                let attained = cha >= title.req;
                let progressPct = attained ? 100 : ((cha / title.req) * 100).toFixed(2);
                row.innerHTML = `
                    <td>${title.level}</td>
                    <td>${title.name}</td>
                    <td>${title.req}</td>
                    <td>${cha.toFixed(2)}</td>
                    <td>${title.reward}</td>
                    <td>
                        ${attained ? '<span style="color: green; font-weight: bold;">已获得</span>' : `
                        <div class="progress-container"><div class="progress-bar" style="width: ${progressPct}%;"></div></div>
                        ${progressPct}%`}
                    </td>
                `;
                tbChar.appendChild(row);
            });
            // 阅识称号
            let tbRead = document.getElementById('titlesReadExpTable').querySelector('tbody');
            tbRead.innerHTML = '';
            let re = data.attributes.readExp;
            TITLES_READ_EXP.forEach(title => {
                let row = document.createElement('tr');
                let attained = re >= title.req;
                let progressPct = attained ? 100 : ((re / title.req) * 100).toFixed(2);
                row.innerHTML = `
                    <td>${title.level}</td>
                    <td>${title.name}</td>
                    <td>${title.req}</td>
                    <td>${re.toFixed(2)}</td>
                    <td>${title.reward}</td>
                    <td>
                        ${attained ? '<span style="color: green; font-weight: bold;">已获得</span>' : `
                        <div class="progress-container"><div class="progress-bar" style="width: ${progressPct}%;"></div></div>
                        ${progressPct}%`}
                    </td>
                `;
                tbRead.appendChild(row);
            });
        }

        /*********************************
         * “阶段性总结” 功能实现 *
         *********************************/
        // 为简化示例，各图表初次渲染后，只展示当下数据。用户可点击按钮切换视图，但具体刷新逻辑可依据需求自行扩展。
        let basicMonthlyChart, basicYearlyChart, basicAllChart;
        let artMonthlyChart, artYearlyChart, artAllChart;
        function initCharts() {
            // 基础属性：绘制智力、知识、阅识、体力、意志、魅力随天数变化的折线图
            let ctxBM = document.getElementById('basicMonthlyChart').getContext('2d');
            basicMonthlyChart = new Chart(ctxBM, {
                type: 'line',
                data: {
                    labels: [], datasets: [
                        { label: '智力', data: [], fill: false },
                        { label: '知识', data: [], fill: false },
                        { label: '阅识', data: [], fill: false },
                        { label: '体力', data: [], fill: false },
                        { label: '意志', data: [], fill: false },
                        { label: '魅力', data: [], fill: false }
                    ]
                },
                options: {
                    responsive: true,
                    plugins: { legend: { position: 'bottom' } },
                    scales: { x: { title: { display: true, text: '日期' } }, y: { title: { display: true, text: '属性值' } } }
                }
            });
            let ctxBY = document.getElementById('basicYearlyChart').getContext('2d');
            basicYearlyChart = new Chart(ctxBY, {
                type: 'bar',
                data: { labels: [], datasets: [] },
                options: {
                    responsive: true,
                    plugins: { legend: { position: 'bottom' } },
                    scales: { x: { title: { display: true, text: '日期/月份' } }, y: { title: { display: true, text: '属性值' } } }
                }
            });
            let ctxBA = document.getElementById('basicAllChart').getContext('2d');
            basicAllChart = new Chart(ctxBA, {
                type: 'bar',
                data: { labels: [], datasets: [] },
                options: {
                    responsive: true,
                    plugins: { legend: { position: 'bottom' } },
                    scales: { x: { title: { display: true, text: '月份/年份' } }, y: { title: { display: true, text: '属性值' } } }
                }
            });
            // 幻构师经验
            let ctxAM = document.getElementById('artMonthlyChart').getContext('2d');
            artMonthlyChart = new Chart(ctxAM, {
                type: 'line',
                data: { labels: [], datasets: [{ label: '幻构师经验', data: [], fill: false }] },
                options: {
                    responsive: true,
                    plugins: { legend: { position: 'bottom' } },
                    scales: { x: { title: { display: true, text: '日期' } }, y: { title: { display: true, text: '经验值' } } }
                }
            });
            let ctxAY = document.getElementById('artYearlyChart').getContext('2d');
            artYearlyChart = new Chart(ctxAY, {
                type: 'bar',
                data: { labels: [], datasets: [{ label: '幻构师经验', data: [] }] },
                options: {
                    responsive: true,
                    plugins: { legend: { position: 'bottom' } },
                    scales: { x: { title: { display: true, text: '日期/月份' } }, y: { title: { display: true, text: '经验值' } } }
                }
            });
            let ctxAA = document.getElementById('artAllChart').getContext('2d');
            artAllChart = new Chart(ctxAA, {
                type: 'bar',
                data: { labels: [], datasets: [{ label: '幻构师经验', data: [] }] },
                options: {
                    responsive: true,
                    plugins: { legend: { position: 'bottom' } },
                    scales: { x: { title: { display: true, text: '月份/年份' } }, y: { title: { display: true, text: '经验值' } } }
                }
            });
            updateCharts();
        }
        function updateCharts() {
            // 获取当月所有天的属性数据与 artExp 数据
            let today = new Date();
            let beijing = new Date(today.getTime() + (today.getTimezoneOffset() * 60000) + (8 * 3600000));
            let year = beijing.getFullYear();
            let month = beijing.getMonth(); // 0-11
            let daysInMonth = new Date(year, month + 1, 0).getDate();
            let labels = [];
            let intData = [], knData = [], reData = [], stData = [], wiData = [], chData = [], artData = [];
            for (let d = 1; d <= daysInMonth; d++) {
                let dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(d).padStart(2, '0')}`;
                labels.push(String(d));
                // 计算到该日为止的属性与 artExp
                let sumInts = 0, sumKns = 0, sumRes = 0, sumSts = 0, sumWis = 0, sumArts = 1460;
                data.dailyRecords.filter(r => r.date <= dateStr).forEach(r => {
                    sumArts += r.deltas.artExpDelta;
                    sumInts += r.deltas.intDelta;
                    sumKns += r.deltas.knowledgeDelta;
                    sumRes += r.deltas.readExpDelta;
                    sumSts += r.deltas.staminaDelta;
                    sumWis += r.deltas.willDelta;
                });
                let sumChs = 0.1 * (sumInts + sumKns + sumRes + sumSts + sumWis);
                intData.push(sumInts.toFixed(2));
                knData.push(sumKns.toFixed(2));
                reData.push(sumRes.toFixed(2));
                stData.push(sumSts.toFixed(2));
                wiData.push(sumWis.toFixed(2));
                chData.push(sumChs.toFixed(2));
                artData.push(sumArts.toFixed(2));
            }
            // 更新 basicMonthlyChart
            basicMonthlyChart.data.labels = labels;
            basicMonthlyChart.data.datasets[0].data = intData;
            basicMonthlyChart.data.datasets[1].data = knData;
            basicMonthlyChart.data.datasets[2].data = reData;
            basicMonthlyChart.data.datasets[3].data = stData;
            basicMonthlyChart.data.datasets[4].data = wiData;
            basicMonthlyChart.data.datasets[5].data = chData;
            basicMonthlyChart.update();
            // 更新 artMonthlyChart
            artMonthlyChart.data.labels = labels;
            artMonthlyChart.data.datasets[0].data = artData;
            artMonthlyChart.update();
            // 年度与全时图表可依需求用月度/年度汇总数据，此处简化为当月数据的缩放显示
            // 年度 charts：显示到当前月为止每月最后一天的数据
            let monthLabels = [];
            let monthArtData = [];
            let monthIntData = [], monthKnData = [], monthReData = [], monthStData = [], monthWiData = [], monthChData = [];
            for (let m = 0; m <= month; m++) {
                let mLastDay = new Date(year, m + 1, 0).getDate();
                let dateStr = `${year}-${String(m + 1).padStart(2, '0')}-${String(mLastDay).padStart(2, '0')}`;
                monthLabels.push(`${m + 1}月`);
                let sumInts = 0, sumKns = 0, sumRes = 0, sumSts = 0, sumWis = 0, sumArts = 1460;
                data.dailyRecords.filter(r => r.date <= dateStr).forEach(r => {
                    sumArts += r.deltas.artExpDelta;
                    sumInts += r.deltas.intDelta;
                    sumKns += r.deltas.knowledgeDelta;
                    sumRes += r.deltas.readExpDelta;
                    sumSts += r.deltas.staminaDelta;
                    sumWis += r.deltas.willDelta;
                });
                let sumChs = 0.1 * (sumInts + sumKns + sumRes + sumSts + sumWis);
                monthArtData.push(sumArts.toFixed(2));
                monthIntData.push(sumInts.toFixed(2));
                monthKnData.push(sumKns.toFixed(2));
                monthReData.push(sumRes.toFixed(2));
                monthStData.push(sumSts.toFixed(2));
                monthWiData.push(sumWis.toFixed(2));
                monthChData.push(sumChs.toFixed(2));
            }
            // 更新 basicYearlyChart
            basicYearlyChart.data.labels = monthLabels;
            basicYearlyChart.data.datasets = [
                { label: '智力', data: monthIntData },
                { label: '知识', data: monthKnData },
                { label: '阅识', data: monthReData },
                { label: '体力', data: monthStData },
                { label: '意志', data: monthWiData },
                { label: '魅力', data: monthChData }
            ];
            basicYearlyChart.update();
            // 更新 artYearlyChart
            artYearlyChart.data.labels = monthLabels;
            artYearlyChart.data.datasets[0].data = monthArtData;
            artYearlyChart.update();
            // 全部历史图：按年份汇总（这里假设只记录当前年数据；如需跨年，则需重写）
            let yearLabel = [`${year}`];
            let yearArtSum = artData[artData.length - 1];
            let yearIntSum = intData[intData.length - 1];
            let yearKnSum = knData[knData.length - 1];
            let yearReSum = reData[reData.length - 1];
            let yearStSum = stData[stData.length - 1];
            let yearWiSum = wiData[wiData.length - 1];
            let yearChSum = chData[chData.length - 1];
            basicAllChart.data.labels = yearLabel;
            basicAllChart.data.datasets = [
                { label: '智力', data: [yearIntSum] },
                { label: '知识', data: [yearKnSum] },
                { label: '阅识', data: [yearReSum] },
                { label: '体力', data: [yearStSum] },
                { label: '意志', data: [yearWiSum] },
                { label: '魅力', data: [yearChSum] }
            ];
            basicAllChart.update();
            artAllChart.data.labels = yearLabel;
            artAllChart.data.datasets[0].data = [yearArtSum];
            artAllChart.update();
        }
        document.getElementById('toggleBasicMonthly').addEventListener('click', () => {
            document.getElementById('basicMonthlyChart').parentElement.style.display = 'block';
            document.getElementById('basicYearlyChart').parentElement.style.display = 'none';
            document.getElementById('basicAllChart').parentElement.style.display = 'none';
        });
        document.getElementById('toggleBasicYearly').addEventListener('click', () => {
            document.getElementById('basicMonthlyChart').parentElement.style.display = 'none';
            document.getElementById('basicYearlyChart').parentElement.style.display = 'block';
            document.getElementById('basicAllChart').parentElement.style.display = 'none';
        });
        document.getElementById('toggleBasicAll').addEventListener('click', () => {
            document.getElementById('basicMonthlyChart').parentElement.style.display = 'none';
            document.getElementById('basicYearlyChart').parentElement.style.display = 'none';
            document.getElementById('basicAllChart').parentElement.style.display = 'block';
        });
        document.getElementById('toggleArtMonthly').addEventListener('click', () => {
            document.getElementById('artMonthlyChart').parentElement.style.display = 'block';
            document.getElementById('artYearlyChart').parentElement.style.display = 'none';
            document.getElementById('artAllChart').parentElement.style.display = 'none';
        });
        document.getElementById('toggleArtYearly').addEventListener('click', () => {
            document.getElementById('artMonthlyChart').parentElement.style.display = 'none';
            document.getElementById('artYearlyChart').parentElement.style.display = 'block';
            document.getElementById('artAllChart').parentElement.style.display = 'none';
        });
        document.getElementById('toggleArtAll').addEventListener('click', () => {
            document.getElementById('artMonthlyChart').parentElement.style.display = 'none';
            document.getElementById('artYearlyChart').parentElement.style.display = 'none';
            document.getElementById('artAllChart').parentElement.style.display = 'block';
        });

        /******************************
         * “任务系统” 功能实现 *
         ******************************/
        let nextTaskId = 1;
        function renderTasks() {
            let pendingBody = document.getElementById('pendingTasksBody');
            pendingBody.innerHTML = '';
            let completedBody = document.getElementById('completedTasksBody');
            completedBody.innerHTML = '';
            data.tasks.forEach(task => {
                // 计算进度值
                let currentVal = 0;
                // 根据目标类型，从 dailyRecords 累计值
                data.dailyRecords.forEach(r => {
                    switch (task.targetType) {
                        case 'study': currentVal += r.studyMin; break;
                        case 'specialRead': currentVal += r.specialReadPages; break;
                        case 'art': currentVal += r.artMin; break;
                        case 'exercise': currentVal += r.exerciseMin; break;
                        case 'bookRead': currentVal += r.bookReadMin; break;
                        case 'videoWatch': currentVal += r.videoWatchMin; break;
                        default: break;
                    }
                });
                task.progressValue = currentVal;
                // 渲染行
                let tr = document.createElement('tr');
                let remainingDays = task.deadline ? (daysBetween(new Date(), parseDate(task.deadline)) - 1) : '-';
                let progressPct = Math.min((currentVal / task.targetValue) * 100, 100).toFixed(2);
                let rewardPenaltyStr = '';
                task.rewards.forEach(rw => rewardPenaltyStr += `奖励[${rw.type}:${rw.value}] `);
                task.penalties.forEach(pn => rewardPenaltyStr += `惩罚[${pn.type}:${pn.value}] `);
                if (task.status === 'pending') {
                    tr.innerHTML = `
                        <td>${task.name}</td>
                        <td>${task.type}</td>
                        <td>${currentVal}/${task.targetValue}</td>
                        <td>${remainingDays >= 0 ? remainingDays : '已过期'}</td>
                        <td>${rewardPenaltyStr}</td>
                        <td>
                            <div class="progress-container"><div class="progress-bar" style="width: ${progressPct}%;"></div></div>
                            ${progressPct}%
                        </td>
                        <td>
                            <button class="action-btn complete-btn" data-id="${task.id}">完成</button>
                            <button class="action-btn delete-btn" data-id="${task.id}">删除</button>
                        </td>
                    `;
                    pendingBody.appendChild(tr);
                } else if (task.status === 'completed') {
                    tr.innerHTML = `
                        <td>${task.name}</td>
                        <td>${task.type}</td>
                        <td>${task.targetValue}/${task.targetValue}</td>
                        <td>${task.completionDate}</td>
                        <td>${rewardPenaltyStr}</td>
                        <td>
                            <button class="action-btn delete-btn" data-id="${task.id}">删除</button>
                        </td>
                    `;
                    completedBody.appendChild(tr);
                }
            });
            // 绑定按钮事件
            document.querySelectorAll('.complete-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    completeTask(parseInt(btn.dataset.id));
                });
            });
            document.querySelectorAll('.delete-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    deleteTask(parseInt(btn.dataset.id));
                });
            });
        }
        function addPenaltyOrReward(containerId, type) {
            let container = document.getElementById(containerId);
            let div = document.createElement('div');
            div.classList.add('form-inline');
            div.innerHTML = `
                <div class="form-group">
                    <select class="${type}TypeSelect" required>
                        <option value="">类型</option>
                        <option value="intelligence">智力</option>
                        <option value="knowledge">知识</option>
                        <option value="stamina">体力</option>
                        <option value="will">意志</option>
                        <option value="charisma">魅力</option>
                        <option value="artExp">幻构师经验</option>
                    </select>
                </div>
                <div class="form-group">
                    <input type="number" class="${type}ValueInput" min="0" placeholder="值" required>
                </div>
                <button type="button" class="remove-btn">删除</button>
            `;
            container.appendChild(div);
            div.querySelector('.remove-btn').addEventListener('click', () => {
                container.removeChild(div);
            });
        }
        document.getElementById('addPenaltyBtn').addEventListener('click', () => addPenaltyOrReward('penaltyContainer', 'penalty'));
        document.getElementById('addRewardBtn').addEventListener('click', () => addPenaltyOrReward('rewardContainer', 'reward'));
        function createTask() {
            let name = document.getElementById('taskName').value.trim();
            let description = document.getElementById('taskDescription').value.trim();
            let type = document.getElementById('taskType').value;
            let cycle = document.getElementById('taskCycle').value;
            let targetType = document.getElementById('taskTargetType').value;
            let targetValue = parseInt(document.getElementById('taskTargetValue').value);
            let deadline = document.getElementById('taskDeadline').value;
            if (!name || !type || !cycle || !targetType || isNaN(targetValue) || targetValue < 1) {
                alert('请完整填写必填项且目标数值≥1');
                return;
            }
            let penalties = [], rewards = [];
            document.querySelectorAll('#penaltyContainer .form-inline').forEach(div => {
                let select = div.querySelector('.penaltyTypeSelect');
                let valueInput = div.querySelector('.penaltyValueInput');
                let val = parseFloat(valueInput.value);
                if (select.value && !isNaN(val) && val >= 0) {
                    penalties.push({ type: select.value, value: val });
                }
            });
            document.querySelectorAll('#rewardContainer .form-inline').forEach(div => {
                let select = div.querySelector('.rewardTypeSelect');
                let valueInput = div.querySelector('.rewardValueInput');
                let val = parseFloat(valueInput.value);
                if (select.value && !isNaN(val) && val >= 0) {
                    rewards.push({ type: select.value, value: val });
                }
            });
            let newTask = {
                id: nextTaskId++,
                name, description, type, cycle, targetType, targetValue, deadline,
                penalties, rewards,
                createdDate: formatDate(new Date()),
                status: 'pending',
                progressValue: 0,
                completionDate: ''
            };
            data.tasks.push(newTask);
            renderTasks();
            // 清空表单
            document.getElementById('taskForm').reset();
            document.getElementById('penaltyContainer').querySelectorAll('.form-inline').forEach((div, idx) => {
                if (idx > 0) div.remove();
            });
            document.getElementById('rewardContainer').querySelectorAll('.form-inline').forEach((div, idx) => {
                if (idx > 0) div.remove();
            });
        }
        document.getElementById('createTaskBtn').addEventListener('click', createTask);
        function completeTask(id) {
            let task = data.tasks.find(t => t.id === id);
            if (!task) return;
            if (task.progressValue < task.targetValue) {
                alert('任务尚未达到目标，无法完成');
                return;
            }
            // 奖励生效
            task.rewards.forEach(rw => {
                if (['intelligence', 'knowledge', 'stamina', 'will', 'charisma', 'artExp'].includes(rw.type)) {
                    data.attributes[rw.type] += rw.value;
                }
            });
            task.status = 'completed';
            task.completionDate = formatDate(new Date());
            renderAll();
        }
        function deleteTask(id) {
            if (!confirm('确定删除该任务？')) return;
            data.tasks = data.tasks.filter(t => t.id !== id);
            renderAll();
        }

        /*****************************
         * “规则说明” 弹出框逻辑 *
         *****************************/
        document.getElementById('statusInfoBtn').addEventListener('click', () => {
            document.getElementById('statusInfoModal').style.display = 'flex';
        });
        document.getElementById('statusInfoClose').addEventListener('click', () => {
            document.getElementById('statusInfoModal').style.display = 'none';
        });
        window.addEventListener('click', e => {
            if (e.target === document.getElementById('statusInfoModal')) {
                document.getElementById('statusInfoModal').style.display = 'none';
            }
        });

        /****************************
         * 渲染所有部分的主函数 *
         ****************************/
        function renderAll() {
            renderStatus();
            renderDailyHistory();
            renderDawnHistory();
            renderSummaryHistory();
            renderOverallHistory();
            renderArtPlan();
            renderTruthPlan();
            renderTitles();
            renderTasks();
            updateCharts();
        }

        // 初始加载数据并渲染
        loadData();
        initCharts();
        renderAll();
    </script>
</body>
</html>
