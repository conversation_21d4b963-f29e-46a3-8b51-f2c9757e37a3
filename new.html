<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人生游戏计划</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #4361ee;
            --primary-dark: #3a56d4;
            --secondary: #7209b7;
            --accent: #f72585;
            --success: #4cc9f0;
            --warning: #f8961e;
            --danger: #e63946;
            --light: #f8f9fa;
            --dark: #212529;
            --gray: #6c757d;
            --card-bg: rgba(255, 255, 255, 0.85);
            --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #1a2a6c, #b21f1f, #1a2a6c);
            background-size: 400% 400%;
            animation: gradientBG 15s ease infinite;
            color: var(--dark);
            line-height: 1.6;
            min-height: 100vh;
            padding: 20px;
        }
        
        @keyframes gradientBG {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        /* 头部样式 */
        header {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        .header-left {
            display: flex;
            flex-direction: column;
        }
        
        .datetime {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 5px;
        }
        
        .days {
            font-size: 1rem;
            color: var(--gray);
            background: rgba(67, 97, 238, 0.1);
            padding: 4px 12px;
            border-radius: 20px;
            display: inline-block;
        }
        
        .header-buttons {
            display: flex;
            gap: 12px;
        }
        
        .header-btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
        }
        
        .header-btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
        }
        
        .header-btn.secondary {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(5px);
            color: var(--dark);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .header-btn.secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        /* 状态卡片 */
        .status-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 25px;
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(255, 255, 255, 0.18);
            transition: var(--transition);
        }
        
        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .card-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .card-icon {
            width: 40px;
            height: 40px;
            background: rgba(67, 97, 238, 0.1);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary);
            font-size: 1.2rem;
        }
        
        .info-icon {
            width: 32px;
            height: 32px;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .info-icon:hover {
            background: var(--primary);
            color: white;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.03);
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            color: var(--gray);
        }
        
        .status-value {
            font-weight: 600;
            color: var(--dark);
        }
        
        .progress-container {
            margin-top: 8px;
            height: 6px;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            background: var(--primary);
            border-radius: 3px;
        }
        
        /* 选项卡 */
        .tabs-container {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            overflow: hidden;
            margin-bottom: 30px;
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        .tabs-header {
            display: flex;
            overflow-x: auto;
            padding: 0 20px;
            background: rgba(255, 255, 255, 0.5);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .tab {
            padding: 18px 25px;
            cursor: pointer;
            font-weight: 600;
            color: var(--gray);
            position: relative;
            transition: var(--transition);
            white-space: nowrap;
        }
        
        .tab.active {
            color: var(--primary);
        }
        
        .tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: var(--primary);
            border-radius: 3px 3px 0 0;
        }
        
        .tab:hover:not(.active) {
            color: var(--primary-dark);
            background: rgba(67, 97, 238, 0.05);
        }
        
        .tab-content {
            padding: 30px;
            min-height: 400px;
        }
        
        .tab-pane {
            display: none;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        /* 表单样式 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--dark);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-input {
            width: 100%;
            padding: 14px 18px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.7);
            font-size: 1rem;
            transition: var(--transition);
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.2);
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 12px;
        }
        
        .checkbox-group input {
            width: 20px;
            height: 20px;
            accent-color: var(--primary);
        }
        
        .btn {
            background: var(--primary);
            color: white;
            border: none;
            padding: 14px 28px;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 4px 15px rgba(67, 97, 238, 0.3);
        }
        
        .btn:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(5px);
            color: var(--dark);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        /* 表格样式 */
        .table-container {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(255, 255, 255, 0.18);
            overflow-x: auto;
            margin-top: 25px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1000px;
        }
        
        th {
            background: rgba(67, 97, 238, 0.1);
            padding: 16px 20px;
            text-align: left;
            font-weight: 600;
            color: var(--primary);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        td {
            padding: 14px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.03);
        }
        
        tr:last-child td {
            border-bottom: none;
        }
        
        tr:hover {
            background: rgba(67, 97, 238, 0.03);
        }
        
        .action-btn {
            padding: 6px 12px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: var(--transition);
            font-size: 0.9rem;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        
        .action-btn.edit {
            background: rgba(67, 97, 238, 0.1);
            color: var(--primary);
        }
        
        .action-btn.edit:hover {
            background: var(--primary);
            color: white;
        }
        
        .action-btn.delete {
            background: rgba(231, 57, 70, 0.1);
            color: var(--danger);
        }
        
        .action-btn.delete:hover {
            background: var(--danger);
            color: white;
        }
        
        /* 图表容器 */
        .chart-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }
        
        .chart-card {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 25px;
            box-shadow: var(--card-shadow);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        .chart-placeholder {
            height: 300px;
            background: rgba(0, 0, 0, 0.02);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray);
            font-size: 1.2rem;
            margin-top: 15px;
        }
        
        /* 响应式设计 */
        @media (max-width: 992px) {
            .header {
                flex-direction: column;
                gap: 15px;
            }
            
            .header-buttons {
                width: 100%;
                flex-wrap: wrap;
            }
            
            .header-btn {
                flex: 1;
                min-width: 120px;
            }
        }
        
        @media (max-width: 768px) {
            .chart-container {
                grid-template-columns: 1fr;
            }
            
            .tabs-header {
                padding: 0 10px;
            }
            
            .tab {
                padding: 15px 20px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页眉 -->
        <header>
            <div class="header-left">
                <div class="datetime">2023年11月15日 星期三 14:30:25</div>
                <div class="days">计划启动天数：148天</div>
            </div>
            <div class="header-buttons">
                <button class="header-btn"><i class="fas fa-calendar-alt"></i> 设置开始日期</button>
                <button class="header-btn"><i class="fas fa-file-import"></i> 导入</button>
                <button class="header-btn"><i class="fas fa-file-export"></i> 导出</button>
                <button class="header-btn"><i class="fas fa-save"></i> 保存</button>
                <button class="header-btn secondary"><i class="fas fa-redo"></i> 重置</button>
            </div>
        </header>
        
        <!-- 状态卡片 -->
        <div class="status-section">
            <div class="status-card">
                <div class="card-header">
                    <div class="card-title">
                        <div class="card-icon"><i class="fas fa-chart-line"></i></div>
                        属性状态
                    </div>
                    <div class="info-icon"><i class="fas fa-info"></i></div>
                </div>
                <div class="status-item">
                    <span class="status-label">智力：</span>
                    <span class="status-value">24.75</span>
                </div>
                <div class="status-item">
                    <span class="status-label">知识：</span>
                    <span class="status-value">18.30</span>
                </div>
                <div class="status-item">
                    <span class="status-label">阅识：</span>
                    <span class="status-value">42.15</span>
                </div>
                <div class="status-item">
                    <span class="status-label">体力：</span>
                    <span class="status-value">15.60</span>
                </div>
                <div class="status-item">
                    <span class="status-label">意志：</span>
                    <span class="status-value">32.45</span>
                </div>
                <div class="status-item">
                    <span class="status-label">魅力：</span>
                    <span class="status-value">8.20</span>
                </div>
                <div class="status-item">
                    <span class="status-label">幻构师经验：</span>
                    <span class="status-value">1,860</span>
                </div>
            </div>
            
            <div class="status-card">
                <div class="card-header">
                    <div class="card-title">
                        <div class="card-icon"><i class="fas fa-briefcase"></i></div>
                        职业水平
                    </div>
                    <div class="info-icon"><i class="fas fa-info"></i></div>
                </div>
                <div class="status-item">
                    <span class="status-label">幻构师等级：</span>
                    <span class="status-value">Lv.1 描形学徒（初级）</span>
                </div>
                <div class="status-item">
                    <span class="status-label">真理之路（知识侧）：</span>
                    <span class="status-value">LV.1 灰袍学徒（初级）</span>
                </div>
                <div class="status-item">
                    <span class="status-label">真理之路（智力侧）：</span>
                    <span class="status-value">LV.1 褐衣明理（初级）</span>
                </div>
                <div class="progress-container">
                    <div class="progress-bar" style="width: 65%"></div>
                </div>
            </div>
            
            <div class="status-card">
                <div class="card-header">
                    <div class="card-title">
                        <div class="card-icon"><i class="fas fa-award"></i></div>
                        称号系统
                    </div>
                    <div class="info-icon"><i class="fas fa-info"></i></div>
                </div>
                <div class="status-item">
                    <span class="status-label">晨曦之约称号：</span>
                    <span class="status-value">星辉学徒</span>
                </div>
                <div class="status-item">
                    <span class="status-label">阅识称号：</span>
                    <span class="status-value">历尘星火</span>
                </div>
                <div class="status-item">
                    <span class="status-label">意志称号：</span>
                    <span class="status-value">晨曦微志</span>
                </div>
                <div class="status-item">
                    <span class="status-label">魅力称号：</span>
                    <span class="status-value">萤火微光</span>
                </div>
                <div class="progress-container">
                    <div class="progress-bar" style="width: 45%"></div>
                </div>
            </div>
        </div>
        
        <!-- 选项卡 -->
        <div class="tabs-container">
            <div class="tabs-header">
                <div class="tab active">每日记录</div>
                <div class="tab">晨曦之约计划</div>
                <div class="tab">每日个人总结</div>
                <div class="tab">每日总记录</div>
                <div class="tab">幻构师计划</div>
                <div class="tab">真理之路计划</div>
                <div class="tab">称号系统</div>
                <div class="tab">任务系统</div>
                <div class="tab">阶段性总结</div>
            </div>
            
            <div class="tab-content">
                <!-- 每日记录 -->
                <div class="tab-pane active">
                    <h2>每日活动记录</h2>
                    <p class="status-label">记录您今天的各项活动数据</p>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label"><i class="fas fa-paint-brush"></i> 绘画时长（分钟）</label>
                            <input type="number" class="form-input" value="120">
                        </div>
                        <div class="form-group">
                            <label class="form-label"><i class="fas fa-book"></i> 学习时长（分钟）</label>
                            <input type="number" class="form-input" value="90">
                        </div>
                        <div class="form-group">
                            <label class="form-label"><i class="fas fa-book-open"></i> 阅读专著（页数）</label>
                            <input type="number" class="form-input" value="35">
                        </div>
                        <div class="form-group">
                            <label class="form-label"><i class="fas fa-running"></i> 运动时长（分钟）</label>
                            <input type="number" class="form-input" value="45">
                        </div>
                        <div class="form-group">
                            <label class="form-label"><i class="fas fa-book-reader"></i> 阅书页数（页数）</label>
                            <input type="number" class="form-input" value="50">
                        </div>
                        <div class="form-group">
                            <label class="form-label"><i class="fas fa-film"></i> 阅影时长（分钟）</label>
                            <input type="number" class="form-input" value="60">
                        </div>
                    </div>
                    
                    <h3>意志参与</h3>
                    <div class="form-grid">
                        <div class="checkbox-group">
                            <input type="checkbox" id="chkWillArt" checked>
                            <label for="chkWillArt">绘画参与意志</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="chkWillStudy" checked>
                            <label for="chkWillStudy">学习参与意志</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="chkWillExercise">
                            <label for="chkWillExercise">运动参与意志</label>
                        </div>
                        <div class="checkbox-group">
                            <input type="checkbox" id="chkWillBook" checked>
                            <label for="chkWillBook">阅书参与意志</label>
                        </div>
                        <div class="checkbox-group