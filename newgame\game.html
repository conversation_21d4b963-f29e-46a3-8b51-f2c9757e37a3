<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>人生游戏计划</title>
    <style>
        /* ===== 全局样式 ===== */
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body {
            font-family: "Arial", sans-serif;
            background-color: #f0f2f5;
            color: #333;
        }
        header {
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            color: #fff;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        header .left {
            display: flex;
            flex-direction: column;
        }
        header .left .datetime {
            font-size: 1.2em;
        }
        header .left .days {
            font-size: 0.9em;
            margin-top: 4px;
        }
        header .right button {
            margin-left: 12px;
            padding: 6px 12px;
            font-size: 0.9em;
            border: none;
            border-radius: 4px;
            background-color: #fff;
            color: #4facfe;
            cursor: pointer;
            transition: background 0.2s;
        }
        header .right button:hover {
            background-color: #e0e0e0;
        }
        .container {
            padding: 20px;
        }

        /* ===== “当前状态” 样式 ===== */
        .status-card {
            background-color: #fff;
            border-radius: 6px;
            padding: 16px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 24px;
            position: relative;
        }
        .status-card h2 {
            font-size: 1.2em;
            margin-bottom: 12px;
        }
        .status-card .question-mark {
            position: absolute;
            top: 16px;
            right: 20px;
            cursor: pointer;
            font-size: 1.2em;
            color: #4facfe;
        }
        .status-section {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        .status-box {
            background-color: #fafafa;
            border-radius: 4px;
            padding: 12px;
            flex: 1 1 200px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .status-box h3 {
            margin-bottom: 8px;
            font-size: 1em;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 4px;
        }
        .status-box p {
            font-size: 0.9em;
            margin: 6px 0;
        }

        /* ===== 选项卡导航 ===== */
        .tabs {
            display: flex;
            border-bottom: 2px solid #ddd;
            margin-bottom: 16px;
        }
        .tab {
            padding: 10px 16px;
            cursor: pointer;
            background-color: #eee;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            margin-right: 4px;
            transition: background 0.2s;
        }
        .tab.active {
            background-color: #fff;
            border: 1px solid #ddd;
            border-bottom: none;
            font-weight: bold;
        }
        .tab:hover {
            background-color: #ddd;
        }
        .tab-content {
            display: none;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 0 4px 4px 4px;
            padding: 16px;
        }
        .tab-content.active {
            display: block;
        }

        /* ===== 阶段性总结样式 ===== */
        .charts-container {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }
        .chart-section {
            background-color: #fff;
            border-radius: 6px;
            padding: 16px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .chart-section h3 {
            font-size: 1.2em;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
        }
        .chart-wrapper {
            width: 100%;
            height: 400px;
            margin-bottom: 16px;
        }
        .chart-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            padding: 12px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .chart-type-toggle,
        .time-range-toggle {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .time-range-toggle h4 {
            font-size: 0.9em;
            margin: 0;
            color: #666;
        }
        .toggle-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #fff;
            color: #666;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.2s;
        }
        .toggle-btn:hover {
            background-color: #f0f0f0;
        }
        .toggle-btn.active {
            background-color: #4facfe;
            color: #fff;
            border-color: #4facfe;
        }

        /* ===== 通用表单 & 表格样式 ===== */
        form {
            margin-bottom: 16px;
        }
        .form-group {
            margin-bottom: 12px;
        }
        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-size: 0.9em;
        }
        .form-group input[type="number"],
        .form-group input[type="text"],
        .form-group input[type="date"],
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .form-inline {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 12px;
        }
        .form-inline .form-group {
            flex: 1 1 150px;
        }
        form button {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            background-color: #4facfe;
            color: #fff;
            cursor: pointer;
            transition: background 0.2s;
            font-size: 0.9em;
        }
        form button:hover {
            background-color: #3b9dd8;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 12px;
            table-layout: auto;
        }
        table th, table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
            font-size: 0.85em;
        }
        table th {
            background-color: #f5f5f5;
        }
        .pagination {
            display: flex;
            justify-content: center;
            gap: 6px;
            margin-top: 8px;
        }
        .pagination button {
            padding: 4px 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #fff;
            cursor: pointer;
            font-size: 0.85em;
        }
        .pagination button.active {
            background-color: #4facfe;
            color: #fff;
            border-color: #4facfe;
        }

        /* ===== 进度条样式 ===== */
        .progress-container {
            background-color: #eee;
            border-radius: 4px;
            overflow: hidden;
            height: 14px;
        }
        .progress-bar {
            height: 100%;
            background-color: #4facfe;
            width: 0%;
            transition: width 0.3s;
        }

        /* ===== 弹出框（规则说明 & 编辑） ===== */
        .modal {
            display: none;
            position: fixed;
            top: 0; left: 0;
            width: 100%; height: 100%;
            background: rgba(0,0,0,0.4);
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        .modal-content {
            background-color: #fff;
            width: 80%;
            max-height: 90%;
            overflow-y: auto;
            border-radius: 6px;
            padding: 20px;
            position: relative;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        .modal-close {
            position: absolute;
            top: 12px;
            right: 16px;
            cursor: pointer;
            font-size: 1.2em;
            color: #888;
        }
        .modal-close:hover {
            color: #333;
        }

        /* ===== 编辑模态框样式 ===== */
        .edit-modal .modal-content {
            max-width: 600px;
        }
        .edit-modal .form-inline {
            flex-direction: column;
            gap: 8px;
        }
        .edit-modal .form-inline .form-group {
            width: 100%;
            margin-bottom: 8px;
        }
        .edit-modal h3 {
            margin-bottom: 12px;
            font-size: 1.1em;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 6px;
        }
        .edit-modal .form-group.date-group {
            margin-bottom: 8px;
        }
        .edit-modal button.save-btn {
            background-color: #28a745;
        }
        .edit-modal button.save-btn:hover {
            background-color: #218838;
        }

        /* ===== “编辑任务” 弹窗样式 ===== */
        .edit-task-modal .modal-content {
            max-width: 650px;
        }
        .edit-task-modal h3 {
            margin-bottom: 12px;
            font-size: 1.1em;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 6px;
        }
        .edit-task-modal .form-inline {
            flex-direction: column;
            gap: 12px;
        }
        .edit-task-modal .form-group {
            width: 100%;
        }
        .edit-task-modal button.save-task-btn {
            background-color: #007bff;
        }
        .edit-task-modal button.save-task-btn:hover {
            background-color: #0069d9;
        }
    </style>
</head>
<body>
    <!-- ===== 页眉：显示当前北京时间 & 计划启动天数 & 保存/导入/导出/重置 ===== -->
    <header>
        <div class="left">
            <div class="datetime" id="currentDatetime">加载中...</div>
            <div class="days" id="planDays">计划启动天数：0</div>
        </div>
        <div class="right">
            <button id="importBtn">导入</button>
            <button id="exportBtn">导出</button>
            <button id="saveBtn">保存</button>
            <button id="resetBtn">重置</button>
        </div>
    </header>

    <div class="container">
        <!-- ===== 当前状态 区域 ===== -->
        <div class="status-card">
            <h2>当前状态 <span class="question-mark" id="statusInfoBtn">?</span></h2>
            <div class="status-section">
                <!-- 属性 -->
                <div class="status-box">
                    <h3>属性</h3>
                    <p>智力：<span id="attrInt">0.00</span></p>
                    <p>知识：<span id="attrKnowledge">0.00</span></p>
                    <p>阅识：<span id="attrReadExp">0.00</span></p>
                    <p>体力：<span id="attrStamina">0.00</span></p>
                    <p>意志：<span id="attrWill">0.00</span></p>
                    <p>魅力：<span id="attrCharisma">0.00</span></p>
                    <p>幻构师经验：<span id="attrArtExp">1460</span></p>
                </div>
                <!-- 职业水平 -->
                <div class="status-box">
                    <h3>职业水平</h3>
                    <p>幻构师等级：<span id="careerArtLevel">Lv.1 描形学徒（初级）</span></p>
                    <p>真理之路（知识侧）：<span id="careerTruthKnowledge">LV.1 灰袍学徒（初级）</span></p>
                    <p>真理之路（智力侧）：<span id="careerTruthIntelligence">LV.1 褐衣明理（初级）</span></p>
                </div>
                <!-- 称号 -->
                <div class="status-box">
                    <h3>称号</h3>
                    <p>晨曦之约称号：<span id="titleDawn">无</span></p>
                    <p>阅识称号：<span id="titleReadExp">无</span></p>
                    <p>意志称号：<span id="titleWill">无</span></p>
                    <p>魅力称号：<span id="titleCharisma">无</span></p>
                </div>
            </div>
        </div>

        <!-- ===== 选项卡导航 ===== -->
        <div class="tabs">
            <div class="tab active" data-tab="dailyRecordTab">每日记录</div>
            <div class="tab" data-tab="dawnTab">晨曦之约计划</div>
            <div class="tab" data-tab="dailySummaryTab">每日个人总结</div>
            <div class="tab" data-tab="dailyOverallTab">每日总记录</div>
            <div class="tab" data-tab="artPlanTab">幻构师计划</div>
            <div class="tab" data-tab="truthPlanTab">真理之路计划</div>
            <div class="tab" data-tab="titlesTab">称号系统</div>
            <div class="tab" data-tab="tasksTab">任务系统</div>
            <div class="tab" data-tab="phaseSummaryTab">阶段性总结</div>
        </div>

        <!-- ===== "阶段性总结" 选项卡内容 ===== -->
        <div class="tab-content" id="phaseSummaryTab">
            <div class="charts-container">
                <!-- 基础属性记录 -->
                <div class="chart-section">
                    <h3>基础属性记录</h3>
                    <div class="chart-wrapper">
                        <canvas id="basicAttrChart"></canvas>
                    </div>
                    <div class="chart-controls">
                        <div class="chart-type-toggle">
                            <button class="toggle-btn active" data-chart="line" data-target="basicAttr">折线图</button>
                            <button class="toggle-btn" data-chart="bar" data-target="basicAttr">条形图</button>
                        </div>
                        <div class="time-range-toggle">
                            <h4>当年趋势</h4>
                            <button class="toggle-btn active" data-range="day" data-target="basicAttr">按天</button>
                            <button class="toggle-btn" data-range="month" data-target="basicAttr">按月</button>
                        </div>
                        <div class="time-range-toggle">
                            <h4>历史趋势</h4>
                            <button class="toggle-btn" data-range="month-history" data-target="basicAttr">按月</button>
                            <button class="toggle-btn" data-range="year" data-target="basicAttr">按年</button>
                        </div>
                    </div>
                </div>

                <!-- 幻构师经验记录 -->
                <div class="chart-section">
                    <h3>幻构师经验记录</h3>
                    <div class="chart-wrapper">
                        <canvas id="artExpChart"></canvas>
                    </div>
                    <div class="chart-controls">
                        <div class="chart-type-toggle">
                            <button class="toggle-btn active" data-chart="line" data-target="artExp">折线图</button>
                            <button class="toggle-btn" data-chart="bar" data-target="artExp">条形图</button>
                        </div>
                        <div class="time-range-toggle">
                            <h4>当年趋势</h4>
                            <button class="toggle-btn active" data-range="day" data-target="artExp">按天</button>
                            <button class="toggle-btn" data-range="month" data-target="artExp">按月</button>
                        </div>
                        <div class="time-range-toggle">
                            <h4>历史趋势</h4>
                            <button class="toggle-btn" data-range="month-history" data-target="artExp">按月</button>
                            <button class="toggle-btn" data-range="year" data-target="artExp">按年</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ===== “每日记录” 选项卡内容 ===== -->
        <div class="tab-content active" id="dailyRecordTab">
            <!-- 表单：输入当日多条记录 -->
            <form id="dailyRecordForm">
                <div class="form-inline">
                    <div class="form-group">
                        <label>绘画时长（分钟）</label>
                        <input type="number" id="inputArtMinutes" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>学习时长（分钟）</label>
                        <input type="number" id="inputStudyMinutes" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>阅读专著页数</label>
                        <input type="number" id="inputSpecialReadPages" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>运动时长（分钟）</label>
                        <input type="number" id="inputExerciseMinutes" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>阅书时长（分钟）</label>
                        <input type="number" id="inputBookReadMinutes" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>阅影时长（分钟）</label>
                        <input type="number" id="inputVideoWatchMinutes" min="0" value="0">
                    </div>
                </div>
                <div class="form-inline">
                    <div class="form-group">
                        <input type="checkbox" id="chkWillArt"><label for="chkWillArt">绘画参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkWillStudy"><label for="chkWillStudy">学习参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkWillExercise"><label for="chkWillExercise">运动参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkWillBook"><label for="chkWillBook">阅书参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkWillVideo"><label for="chkWillVideo">阅影参与意志</label>
                    </div>
                </div>
                <button type="button" id="addDailyRecordBtn">添加记录</button>
            </form>
            <div style="margin-bottom: 8px;">
                <button id="toggleDailyHistoryBtn">历史记录</button>
            </div>
            <div id="dailyHistoryContainer">
                <table id="dailyHistoryTable">
                    <thead>
                        <tr>
                            <th>时间戳</th>
                            <th>学习</th>
                            <th>专著页</th>
                            <th>绘画</th>
                            <th>阅书</th>
                            <th>阅影</th>
                            <th>经验增量</th>
                            <th>智力增量</th>
                            <th>知识增量</th>
                            <th>体力增量</th>
                            <th>意志增量</th>
                            <th>魅力增量</th>
                            <th>称号加成</th>
                            <th>连续统计</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="dailyHistoryBody">
                        <!-- 动态插入 -->
                    </tbody>
                </table>
                <div class="pagination" id="dailyHistoryPagination"></div>
            </div>
        </div>

        <!-- ===== “晨曦之约计划” 选项卡内容 ===== -->
        <div class="tab-content" id="dawnTab">
            <form id="dawnForm">
                <div class="form-group">
                    <input type="checkbox" id="chkSleptOnTime"><label for="chkSleptOnTime">及时入睡</label>
                </div>
                <div class="form-group">
                    <input type="checkbox" id="chkWokeOnTime"><label for="chkWokeOnTime">及时起床</label>
                </div>
                <div class="form-group">
                    <input type="checkbox" id="chkSpecialCase"><label for="chkSpecialCase">特殊情况（算作成功）</label>
                </div>
                <div class="form-inline">
                    <div class="form-group">
                        <input type="checkbox" id="chkEarlySleep"><label for="chkEarlySleep">早睡 +1意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkEarlyRise"><label for="chkEarlyRise">早起 +0.5体力</label>
                    </div>
                </div>
                <button type="button" id="addDawnRecordBtn">打卡</button>
            </form>
            <div style="margin-bottom: 8px;">
                <button id="toggleDawnHistoryBtn">历史记录</button>
            </div>
            <div id="dawnHistoryContainer">
                <table id="dawnHistoryTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>结果</th>
                            <th>早睡</th>
                            <th>早起</th>
                            <th>意志Δ</th>
                            <th>体力Δ</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="dawnHistoryBody">
                        <!-- 动态插入 -->
                    </tbody>
                </table>
                <div class="pagination" id="dawnHistoryPagination"></div>
            </div>
        </div>

        <!-- ===== “每日个人总结” 选项卡内容 ===== -->
        <div class="tab-content" id="dailySummaryTab">
            <form id="dailySummaryForm">
                <div class="form-group">
                    <label>日期</label>
                    <input type="date" id="inputSummaryDate" required>
                </div>
                <div class="form-group">
                    <label>总结内容</label>
                    <textarea id="inputSummaryContent" rows="3"></textarea>
                </div>
                <button type="button" id="addDailySummaryBtn">保存总结</button>
            </form>
            <div style="margin-bottom: 8px;">
                <button id="toggleSummaryHistoryBtn">历史记录</button>
            </div>
            <div id="summaryHistoryContainer">
                <table id="summaryHistoryTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>内容</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="summaryHistoryBody">
                        <!-- 动态插入 -->
                    </tbody>
                </table>
                <div class="pagination" id="summaryHistoryPagination"></div>
            </div>
        </div>

        <!-- ===== “每日总记录” 选项卡内容 ===== -->
        <div class="tab-content" id="dailyOverallTab">
            <div style="margin-bottom: 8px;">
                <button id="toggleOverallHistoryBtn">历史记录</button>
            </div>
            <div id="overallHistoryContainer">
                <table id="overallHistoryTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>幻构师 (当前/Δ)</th>
                            <th>知识 (当前/Δ)</th>
                            <th>智力 (当前/Δ)</th>
                            <th>晨曦打卡</th>
                            <th>总结</th>
                            <th>任务概览</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="overallHistoryBody">
                        <!-- 动态插入 -->
                    </tbody>
                </table>
                <div class="pagination" id="overallHistoryPagination"></div>
            </div>
        </div>

        <!-- ===== “幻构师计划” 选项卡内容 ===== -->
        <div class="tab-content" id="artPlanTab">
            <table id="artPlanTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>总需求经验</th>
                        <th>阶段</th>
                        <th>当前经验</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
        </div>

        <!-- ===== “真理之路计划” 选项卡内容 ===== -->
        <div class="tab-content" id="truthPlanTab">
            <h3>知识侧</h3>
            <table id="truthKnowledgeTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>总需求知识</th>
                        <th>阶段</th>
                        <th>当前知识</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
            <h3 style="margin-top: 16px;">智力侧</h3>
            <table id="truthIntelligenceTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>总需求智力</th>
                        <th>阶段</th>
                        <th>当前智力</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
        </div>

        <!-- ===== “称号系统” 选项卡内容 ===== -->
        <div class="tab-content" id="titlesTab">
            <!-- 晨曦之约称号 -->
            <h3>晨曦之约称号</h3>
            <table id="titlesDawnTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>称号</th>
                        <th>所需坚持天数</th>
                        <th>当前坚持天数</th>
                        <th>奖励</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
            <!-- 阅识称号 -->
            <h3 style="margin-top: 16px;">阅识称号</h3>
            <table id="titlesReadExpTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>称号</th>
                        <th>所需阅识</th>
                        <th>当前阅识</th>
                        <th>奖励</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            <h3 style="margin-top: 16px;">阅识称号</h3>
            <table id="titlesReadExpTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>称号</th>
                        <th>所需阅识</th>
                        <th>当前阅识</th>
                        <th>奖励</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
            <!-- 意志称号 -->
            <h3 style="margin-top: 16px;">意志称号</h3>
            <table id="titlesWillTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>称号</th>
                        <th>所需意志</th>
                        <th>当前意志</th>
                        <th>奖励</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
            <!-- 魅力称号 -->
            <h3 style="margin-top: 16px;">魅力称号</h3>
            <table id="titlesCharismaTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>称号</th>
                        <th>所需魅力</th>
                        <th>当前魅力</th>
                        <th>奖励</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
        </div>

        <!-- ===== “任务系统” 选项卡内容 ===== -->
        <div class="tab-content" id="tasksTab">
            <form id="taskForm">
                <div class="form-group">
                    <label>任务名称</label>
                    <input type="text" id="inputTaskName" required>
                </div>
                <div class="form-group">
                    <label>截止日期</label>
                    <input type="date" id="inputTaskDeadline">
                </div>
                <div class="form-group">
                    <label>奖励（经验/智力/知识/体力/意志/魅力）</label>
                    <input type="text" id="inputTaskRewards" placeholder="格式: exp, int, know, stam, will, cha">
                </div>
                <div class="form-group">
                    <label>惩罚（经验/智力/知识/体力/意志/魅力）</label>
                    <input type="text" id="inputTaskPenalties" placeholder="格式: exp, int, know, stam, will, cha">
                </div>
                <button type="button" id="addTaskBtn">添加任务</button>
            </form>
            <div style="margin-top: 12px;">
                <button id="toggleTaskHistoryBtn">历史任务</button>
            </div>
            <div id="taskHistoryContainer">
                <table id="taskHistoryTable">
                    <thead>
                        <tr>
                            <th>任务名称</th>
                            <th>截止日期</th>
                            <th>奖励</th>
                            <th>惩罚</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="taskHistoryBody">
                        <!-- 动态插入 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- ===== 规则说明弹窗 ===== -->
    <div class="modal" id="rulesModal">
        <div class="modal-content">
            <span class="modal-close" id="closeRulesModal">&times;</span>
            <h3>规则说明</h3>
            <p>以下为各功能的使用规则：</p>
            <ul>
                <li>“每日记录”：记录当日学习、绘画、运动等数据，系统会自动计算对应的属性增量。</li>
                <li>“晨曦之约计划”：每日打卡早睡、早起，可获得额外意志/体力增量。</li>
                <li>“每日个人总结”：可记录每天的文字总结，并查看历史记录。</li>
                <li>“每日总记录”：展示每日属性变化总览、打卡及总结情况。</li>
                <li>“幻构师计划” 与 “真理之路计划”：展示各等级所需经验/知识/智力，并实时更新进度。</li>
                <li>“称号系统”：根据累计阅识/意志/魅力等获取称号及奖励加成。</li>
                <li>“任务系统”：可添加具有截止日期的任务，完成后可领取奖励，若逾期则失败并自动扣除相应惩罚。</li>
            </ul>
        </div>
    </div>

    <!-- ===== 编辑记录弹窗 ===== -->
    <div class="modal edit-modal" id="editRecordModal">
        <div class="modal-content">
            <span class="modal-close" id="closeEditRecordModal">&times;</span>
            <h3>编辑历史记录</h3>
            <div class="form-inline">
                <div class="form-group date-group">
                    <label>时间戳</label>
                    <input type="text" id="editTimestamp" disabled>
                </div>
                <div class="form-group">
                    <label>学习时长（分钟）</label>
                    <input type="number" id="editStudyMinutes" min="0">
                </div>
                <div class="form-group">
                    <label>绘画时长（分钟）</label>
                    <input type="number" id="editArtMinutes" min="0">
                </div>
                <div class="form-group">
                    <label>阅读专著页数</label>
                    <input type="number" id="editSpecialReadPages" min="0">
                </div>
                <div class="form-group">
                    <label>运动时长（分钟）</label>
                    <input type="number" id="editExerciseMinutes" min="0">
                </div>
                <div class="form-group">
                    <label>阅书时长（分钟）</label>
                    <input type="number" id="editBookReadMinutes" min="0">
                </div>
                <div class="form-group">
                    <label>阅影时长（分钟）</label>
                    <input type="number" id="editVideoWatchMinutes" min="0">
                </div>
            </div>
            <div class="form-inline">
                <div class="form-group">
                    <input type="checkbox" id="editChkWillStudy"><label for="editChkWillStudy">学习参与意志</label>
                </div>
                <div class="form-group">
                    <input type="checkbox" id="editChkWillArt"><label for="editChkWillArt">绘画参与意志</label>
                </div>
                <div class="form-group">
                    <input type="checkbox" id="editChkWillExercise"><label for="editChkWillExercise">运动参与意志</label>
                </div>
                <div class="form-group">
                    <input type="checkbox" id="editChkWillBook"><label for="editChkWillBook">阅书参与意志</label>
                </div>
                <div class="form-group">
                    <input type="checkbox" id="editChkWillVideo"><label for="editChkWillVideo">阅影参与意志</label>
                </div>
            </div>
            <button class="save-btn" id="saveEditRecordBtn">保存修改</button>
        </div>
    </div>

    <!-- ===== 编辑任务弹窗 ===== -->
    <div class="modal edit-task-modal" id="editTaskModal">
        <div class="modal-content">
            <span class="modal-close" id="closeEditTaskModal">&times;</span>
            <h3>编辑任务</h3>
            <div class="form-inline">
                <div class="form-group">
                    <label>任务名称</label>
                    <input type="text" id="editTaskName">
                </div>
                <div class="form-group">
                    <label>截止日期</label>
                    <input type="date" id="editTaskDeadline">
                </div>
                <div class="form-group">
                    <label>奖励（经验/智力/知识/体力/意志/魅力）</label>
                    <input type="text" id="editTaskRewards">
                </div>
                <div class="form-group">
                    <label>惩罚（经验/智力/知识/体力/意志/魅力）</label>
                    <input type="text" id="editTaskPenalties">
                </div>
            </div>
            <button class="save-task-btn" id="saveEditTaskBtn">保存修改</button>
        </div>
    </div>

    <!-- ===== JavaScript ===== -->
    <script>
        // 全局数据存储
        const STORAGE_KEY = 'lifeGameData';
        let data = {
            daily: { history: [] },
            dawn: { history: [] },
            summary: { history: [] },
            overall: { history: [] },
            artPlan: [],
            truthKnowledgePlan: [],
            truthIntelligencePlan: [],
            titlesDawn: [],
            titlesReadExp: [],
            titlesWill: [],
            titlesCharisma: [],
            tasks: []
        };

        // 初始化页面
        document.addEventListener('DOMContentLoaded', () => {
            initDateTime();
            loadData();
            renderAll();
            setupEventListeners();
        });

        // 获取并显示当前北京时间及计划启动天数
        function initDateTime() {
            const now = new Date();
            const options = { timeZone: 'Asia/Singapore', hour12: false, hour: '2-digit', minute: '2-digit', second: '2-digit' };
            const formatter = new Intl.DateTimeFormat('zh-CN', options);
            document.getElementById('currentDatetime').textContent = formatter.format(now);
            let savedData = JSON.parse(localStorage.getItem(STORAGE_KEY) || '{}');
            let startDate = savedData.startDate ? new Date(savedData.startDate) : now;
            let diffDays = Math.floor((now - startDate) / (1000 * 60 * 60 * 24));
            document.getElementById('planDays').textContent = `计划启动天数：${diffDays}`;
        }

        // 绑定所有事件
        function setupEventListeners() {
            // 选项卡切换
            document.querySelectorAll('.tab').forEach(tab => {
                tab.addEventListener('click', () => {
                    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    document.querySelectorAll('.tab-content').forEach(tc => tc.classList.remove('active'));
                    document.getElementById(tab.dataset.tab).classList.add('active');
                });
            });

            // 弹窗打开事件
            document.getElementById('statusInfoBtn').addEventListener('click', () => {
                document.getElementById('rulesModal').style.display = 'flex';
            });
            document.getElementById('closeRulesModal').addEventListener('click', () => {
                document.getElementById('rulesModal').style.display = 'none';
            });

            // 添加每日记录
            document.getElementById('addDailyRecordBtn').addEventListener('click', addDailyRecord);
            // 查看/隐藏历史记录
            document.getElementById('toggleDailyHistoryBtn').addEventListener('click', () => {
                const ctr = document.getElementById('dailyHistoryContainer');
                ctr.style.display = ctr.style.display === 'none' ? 'block' : 'none';
            });

            // 添加晨曦打卡记录
            document.getElementById('addDawnRecordBtn').addEventListener('click', addDawnRecord);
            document.getElementById('toggleDawnHistoryBtn').addEventListener('click', () => {
                const ctr = document.getElementById('dawnHistoryContainer');
                ctr.style.display = ctr.style.display === 'none' ? 'block' : 'none';
            });

            // 保存每日总结
            document.getElementById('addDailySummaryBtn').addEventListener('click', addDailySummary);
            document.getElementById('toggleSummaryHistoryBtn').addEventListener('click', () => {
                const ctr = document.getElementById('summaryHistoryContainer');
                ctr.style.display = ctr.style.display === 'none' ? 'block' : 'none';
            });

            // 查看/隐藏每日总记录
            document.getElementById('toggleOverallHistoryBtn').addEventListener('click', () => {
                const ctr = document.getElementById('overallHistoryContainer');
                ctr.style.display = ctr.style.display === 'none' ? 'block' : 'none';
            });

            // 添加任务
            document.getElementById('addTaskBtn').addEventListener('click', addTask);
            document.getElementById('toggleTaskHistoryBtn').addEventListener('click', () => {
                const ctr = document.getElementById('taskHistoryContainer');
                ctr.style.display = ctr.style.display === 'none' ? 'block' : 'none';
            });

            // 导入、导出、保存、重置
            document.getElementById('importBtn').addEventListener('click', importData);
            document.getElementById('exportBtn').addEventListener('click', exportData);
            document.getElementById('saveBtn').addEventListener('click', saveData);
            document.getElementById('resetBtn').addEventListener('click', resetData);

            // 编辑记录弹窗相关
            document.getElementById('closeEditRecordModal').addEventListener('click', () => {
                document.getElementById('editRecordModal').style.display = 'none';
            });
            document.getElementById('saveEditRecordBtn').addEventListener('click', saveEditRecord);
            // 编辑任务弹窗相关
            document.getElementById('closeEditTaskModal').addEventListener('click', () => {
                document.getElementById('editTaskModal').style.display = 'none';
            });
            document.getElementById('saveEditTaskBtn').addEventListener('click', saveEditTask);
        }

        // 加载本地存储数据
        function loadData() {
            let savedData = JSON.parse(localStorage.getItem(STORAGE_KEY) || '{}');
            if (savedData.startDate) data.startDate = savedData.startDate;
            if (savedData.daily) data.daily = savedData.daily;
            if (savedData.dawn) data.dawn = savedData.dawn;
            if (savedData.summary) data.summary = savedData.summary;
            if (savedData.overall) data.overall = savedData.overall;
            if (savedData.artPlan) data.artPlan = savedData.artPlan;
            if (savedData.truthKnowledgePlan) data.truthKnowledgePlan = savedData.truthKnowledgePlan;
            if (savedData.truthIntelligencePlan) data.truthIntelligencePlan = savedData.truthIntelligencePlan;
            if (savedData.titlesDawn) data.titlesDawn = savedData.titlesDawn;
            if (savedData.titlesReadExp) data.titlesReadExp = savedData.titlesReadExp;
            if (savedData.titlesWill) data.titlesWill = savedData.titlesWill;
            if (savedData.titlesCharisma) data.titlesCharisma = savedData.titlesCharisma;
            if (savedData.tasks) data.tasks = savedData.tasks;
        }

        // 保存数据到本地存储
        function saveData() {
            localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
            alert('数据已保存');
        }

        // 导出数据为 JSON 文件
        function exportData() {
            const blob = new Blob([JSON.stringify(data)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'lifeGameData.json';
            a.click();
            URL.revokeObjectURL(url);
        }

        // 导入数据
        function importData() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'application/json';
            input.onchange = e => {
                const file = e.target.files[0];
                const reader = new FileReader();
                reader.onload = evt => {
                    data = JSON.parse(evt.target.result);
                    saveData();
                    renderAll();
                };
                reader.readAsText(file);
            };
            input.click();
        }

        // 重置数据
        function resetData() {
            if (confirm('确定要重置全部数据吗？')) {
                localStorage.removeItem(STORAGE_KEY);
                location.reload();
            }
        }

        // 渲染所有内容
        function renderAll() {
            renderDailyHistory();
            renderDawnHistory();
            renderSummaryHistory();
            renderOverallHistory();
            renderArtPlan();
            renderTruthPlans();
            renderTitles();
            renderTasks();
            updateCurrentAttributes();
        }

        // 渲染每日历史记录表格
        function renderDailyHistory() {
            const tbody = document.getElementById('dailyHistoryBody');
            tbody.innerHTML = '';
            data.daily.history.forEach((rec, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${rec.timestamp}</td>
                    <td>${rec.studyMinutes}</td>
                    <td>${rec.specialReadPages}</td>
                    <td>${rec.artMinutes}</td>
                    <td>${rec.bookReadMinutes}</td>
                    <td>${rec.videoWatchMinutes}</td>
                    <td>${rec.deltas.artExpDelta}</td>
                    <td>${rec.deltas.intelligenceDelta}</td>
                    <td>${rec.deltas.knowledgeDelta}</td>
                    <td>${rec.deltas.staminaDelta}</td>
                    <td>${rec.deltas.willDelta}</td>
                    <td>${rec.deltas.charismaDelta}</td>
                    <td>${rec.titleBonus}</td>
                    <td>${rec.continuousCount}</td>
                    <td>
                        <button onclick="openEditRecordModal(${index})">编辑</button>
                        <button onclick="deleteDailyRecord(${index})">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 渲染晨曦历史记录表格
        function renderDawnHistory() {
            const tbody = document.getElementById('dawnHistoryBody');
            tbody.innerHTML = '';
            data.dawn.history.forEach((rec, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${rec.date}</td>
                    <td>${rec.success ? '成功' : '失败'}</td>
                    <td>${rec.earlySleep ? '是' : '否'}</td>
                    <td>${rec.earlyRise ? '是' : '否'}</td>
                    <td>${rec.willDelta}</td>
                    <td>${rec.staminaDelta}</td>
                    <td>
                        <button onclick="openEditDawnModal(${index})">编辑</button>
                        <button onclick="deleteDawnRecord(${index})">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 渲染个人总结历史表格
        function renderSummaryHistory() {
            const tbody = document.getElementById('summaryHistoryBody');
            tbody.innerHTML = '';
            data.summary.history.forEach((rec, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${rec.date}</td>
                    <td>${rec.content}</td>
                    <td>
                        <button onclick="deleteDailySummary(${index})">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 渲染每日总记录表格
        function renderOverallHistory() {
            const tbody = document.getElementById('overallHistoryBody');
            tbody.innerHTML = '';
            data.overall.history.forEach((rec, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${rec.date}</td>
                    <td>${rec.artExpCurrent}/${rec.artExpDelta}</td>
                    <td>${rec.knowledgeCurrent}/${rec.knowledgeDelta}</td>
                    <td>${rec.intelligenceCurrent}/${rec.intelligenceDelta}</td>
                    <td>${rec.dawnSuccess ? '是' : '否'}</td>
                    <td>${rec.summaryContent ? '已写' : '未写'}</td>
                    <td>${rec.taskOverview}</td>
                    <td>
                        <button onclick="deleteOverallRecord(${index})">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 渲染幻构师计划
        function renderArtPlan() {
            const tbody = document.getElementById('artPlanTable').querySelector('tbody');
            tbody.innerHTML = '';
            data.artPlan.forEach(plan => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${plan.level}</td>
                    <td>${plan.totalExp}</td>
                    <td>${plan.stage}</td>
                    <td>${plan.currentExp}</td>
                    <td>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: ${(plan.currentExp / plan.totalExp) * 100}%;"></div>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 渲染真理之路计划
        function renderTruthPlans() {
            // 知识侧
            const tbK = document.getElementById('truthKnowledgeTable').querySelector('tbody');
            tbK.innerHTML = '';
            data.truthKnowledgePlan.forEach(plan => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${plan.level}</td>
                    <td>${plan.totalKnowledge}</td>
                    <td>${plan.stage}</td>
                    <td>${plan.currentKnowledge}</td>
                    <td>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: ${(plan.currentKnowledge / plan.totalKnowledge) * 100}%;"></div>
                        </div>
                    </td>
                `;
                tbK.appendChild(row);
            });
            // 智力侧
            const tbI = document.getElementById('truthIntelligenceTable').querySelector('tbody');
            tbI.innerHTML = '';
            data.truthIntelligencePlan.forEach(plan => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${plan.level}</td>
                    <td>${plan.totalIntelligence}</td>
                    <td>${plan.stage}</td>
                    <td>${plan.currentIntelligence}</td>
                    <td>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: ${(plan.currentIntelligence / plan.totalIntelligence) * 100}%;"></div>
                        </div>
                    </td>
                `;
                tbI.appendChild(row);
            });
        }

        // 渲染称号
        function renderTitles() {
            // 晨曦之约称号
            const tbD = document.getElementById('titlesDawnTable').querySelector('tbody');
            tbD.innerHTML = '';
            const days = data.dawn.history.length;
            data.titlesDawn.forEach(t => {
                const attained = days >= t.reqDays;
                const pct = attained ? 100 : ((days / t.reqDays) * 100).toFixed(2);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${t.level}</td>
                    <td>${t.name}</td>
                    <td>${t.reqDays}</td>
                    <td>${days}</td>
                    <td>${t.reward}</td>
                    <td>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: ${pct}%;"></div>
                        </div>
                    </td>
                `;
                tbD.appendChild(row);
            });

            // 阅识称号
            const tbRe = document.getElementById('titlesReadExpTable').querySelector('tbody');
            tbRe.innerHTML = '';
            let re = attrs.readExp;
            data.titlesReadExp.forEach(t => {
                const attained = re >= t.req;
                const pct = attained ? 100 : ((re / t.req) * 100).toFixed(2);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${t.level}</td>
                    <td>${t.name}</td>
                    <td>${t.req}</td>
                    <td>${re}</td>
                    <td>${t.reward}</td>
                    <td>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: ${pct}%;"></div>
                        </div>
                    </td>
                `;
                tbRe.appendChild(row);
            });

            // 意志称号
            const tbW = document.getElementById('titlesWillTable').querySelector('tbody');
            tbW.innerHTML = '';
            let wl = attrs.will;
            data.titlesWill.forEach(t => {
                const attained = wl >= t.req;
                const pct = attained ? 100 : ((wl / t.req) * 100).toFixed(2);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${t.level}</td>
                    <td>${t.name}</td>
                    <td>${t.req}</td>
                    <td>${wl}</td>
                    <td>${t.reward}</td>
                    <td>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: ${pct}%;"></div>
                        </div>
                    </td>
                `;
                tbW.appendChild(row);
            });

            // 魅力称号
            const tbC = document.getElementById('titlesCharismaTable').querySelector('tbody');
            tbC.innerHTML = '';
            let ch = attrs.charisma;
            data.titlesCharisma.forEach(t => {
                const attained = ch >= t.req;
                const pct = attained ? 100 : ((ch / t.req) * 100).toFixed(2);
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${t.level}</td>
                    <td>${t.name}</td>
                    <td>${t.req}</td>
                    <td>${ch}</td>
                    <td>${t.reward}</td>
                    <td>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: ${pct}%;"></div>
                        </div>
                    </td>
                `;
                tbC.appendChild(row);
            });
        }

        // 渲染任务
        function renderTasks() {
            const tbody = document.getElementById('taskHistoryTable').querySelector('tbody');
            tbody.innerHTML = '';
            data.tasks.forEach((task, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${task.name}</td>
                    <td>${task.deadline || '无'}</td>
                    <td>${task.rewards.join('/')}</td>
                    <td>${task.penalties.join('/')}</td>
                    <td>${task.status}</td>
                    <td>
                        <button onclick="openEditTaskModal(${index})">编辑</button>
                        <button onclick="deleteTask(${index})">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 更新当前属性值
        let attrs = {
            artExp: 0,
            knowledge: 0,
            intelligence: 0,
            stamina: 0,
            will: 0,
            charisma: 0,
            readExp: 0
        };
        function updateCurrentAttributes() {
            // 重置
            attrs = { artExp: 0, knowledge: 0, intelligence: 0, stamina: 0, will: 0, charisma: 0, readExp: 0 };
            // 1. 累加每日记录中的增量
            data.daily.history.forEach(rec => {
                attrs.artExp += rec.deltas.artExpDelta;
                attrs.intelligence += rec.deltas.intelligenceDelta;
                attrs.knowledge += rec.deltas.knowledgeDelta;
                attrs.stamina += rec.deltas.staminaDelta;
                attrs.will += rec.deltas.willDelta;
                attrs.charisma += rec.deltas.charismaDelta;
                attrs.readExp += rec.deltas.readExpDelta;
            });

            // 2. 累加 “晨曦之约打卡” 中每条打卡带来的 willDelta/staminaDelta
            data.dawn.history.forEach(rec => {
                attrs.will += rec.willDelta;
                attrs.stamina += rec.staminaDelta;
            });

            // 3. 任务系统：检查超期→失败，并累加奖励/惩罚
            let todayStr = formatDate(new Date());
            data.tasks.forEach(task => {
                if (task.status === 'pending' && task.deadline) {
                    let dl = parseDate(task.deadline);
                    let td = parseDate(todayStr);
                    if (td > dl) {
                        task.status = 'failed';
                        // 未获取奖励，扣除惩罚
                        attrs.artExp += task.penalties[0] || 0;
                        attrs.intelligence += task.penalties[1] || 0;
                        attrs.knowledge += task.penalties[2] || 0;
                        attrs.stamina += task.penalties[3] || 0;
                        attrs.will += task.penalties[4] || 0;
                        attrs.charisma += task.penalties[5] || 0;
                    }
                }
            });

            // 4. 根据称号系统额外增益
            data.titlesDawn.forEach(t => {
                const days = data.dawn.history.length;
                if (days >= t.reqDays) {
                    attrs.will += t.bonusWill || 0;
                    attrs.stamina += t.bonusStamina || 0;
                }
            });
            data.titlesReadExp.forEach(t => {
                if (attrs.readExp >= t.req) {
                    attrs.knowledge += t.bonusKnowledge || 0;
                }
            });
            data.titlesWill.forEach(t => {
                if (attrs.will >= t.req) {
                    attrs.will += t.bonusWill || 0;
                }
            });
            data.titlesCharisma.forEach(t => {
                if (attrs.charisma >= t.req) {
                    attrs.charisma += t.bonusCharisma || 0;
                }
            });

            // 5. 更新状态卡片显示
            document.getElementById('attrInt').textContent = attrs.intelligence.toFixed(2);
            document.getElementById('attrKnowledge').textContent = attrs.knowledge.toFixed(2);
            document.getElementById('attrReadExp').textContent = attrs.readExp.toFixed(2);
            document.getElementById('attrStamina').textContent = attrs.stamina.toFixed(2);
            document.getElementById('attrWill').textContent = attrs.will.toFixed(2);
            document.getElementById('attrCharisma').textContent = attrs.charisma.toFixed(2);
        }

        // 辅助函数：格式化日期
        function formatDate(date) {
            const y = date.getFullYear();
            const m = String(date.getMonth() + 1).padStart(2, '0');
            const d = String(date.getDate()).padStart(2, '0');
            return `${y}-${m}-${d}`;
        }

        // 辅助函数：解析日期字符串
        function parseDate(str) {
            return new Date(str + 'T00:00:00');
        }

        // 添加新的每日记录
        function addDailyRecord() {
            const timestamp = new Date().toISOString();
            const studyMinutes = parseInt(document.getElementById('inputStudyMinutes').value) || 0;
            const artMinutes = parseInt(document.getElementById('inputArtMinutes').value) || 0;
            const specialReadPages = parseInt(document.getElementById('inputSpecialReadPages').value) || 0;
            const exerciseMinutes = parseInt(document.getElementById('inputExerciseMinutes').value) || 0;
            const bookReadMinutes = parseInt(document.getElementById('inputBookReadMinutes').value) || 0;
            const videoWatchMinutes = parseInt(document.getElementById('inputVideoWatchMinutes').value) || 0;
            const chkWillStudy = document.getElementById('chkWillStudy').checked;
            const chkWillArt = document.getElementById('chkWillArt').checked;
            const chkWillExercise = document.getElementById('chkWillExercise').checked;
            const chkWillBook = document.getElementById('chkWillBook').checked;
            const chkWillVideo = document.getElementById('chkWillVideo').checked;

            // 计算增量
            const deltas = computeDeltas({
                studyMinutes,
                artMinutes,
                specialReadPages,
                exerciseMinutes,
                bookReadMinutes,
                videoWatchMinutes,
                chkWillStudy,
                chkWillArt,
                chkWillExercise,
                chkWillBook,
                chkWillVideo
            });

            const titleBonus = computeTitleBonus(attrs);
            const continuousCount = computeContinuousCount(data.daily.history);

            const record = {
                timestamp,
                studyMinutes,
                artMinutes,
                specialReadPages,
                exerciseMinutes,
                bookReadMinutes,
                videoWatchMinutes,
                chkWillStudy,
                chkWillArt,
                chkWillExercise,
                chkWillBook,
                chkWillVideo,
                deltas: {
                    artExpDelta: deltas.artExpDelta,
                    intelligenceDelta: deltas.intelligenceDelta,
                    knowledgeDelta: deltas.knowledgeDelta,
                    staminaDelta: deltas.staminaDelta,
                    willDelta: deltas.willDelta,
                    charismaDelta: deltas.charismaDelta,
                    readExpDelta: deltas.readExpDelta
                },
                titleBonus,
                continuousCount
            };
            data.daily.history.push(record);
            updateOverallRecordForDate(formatDate(new Date()));
            renderAll();
            document.getElementById('dailyRecordForm').reset();
        }

        // 计算属性增量
        function computeDeltas({ studyMinutes, artMinutes, specialReadPages, exerciseMinutes, bookReadMinutes, videoWatchMinutes, chkWillStudy, chkWillArt, chkWillExercise, chkWillBook, chkWillVideo }) {
            // 假设的增量逻辑（可按需调整）
            const artExpDelta = artMinutes * 0.1 + (chkWillArt ? 0.5 : 0);
            const intelligenceDelta = studyMinutes * 0.05 + (chkWillStudy ? 0.3 : 0);
            const knowledgeDelta = specialReadPages * 0.2 + (chkWillBook ? 0.2 : 0);
            const staminaDelta = exerciseMinutes * 0.08 + (chkWillExercise ? 0.2 : 0);
            const willDelta = (chkWillStudy || chkWillArt || chkWillExercise || chkWillBook || chkWillVideo) ? 0.5 : 0;
            const charismaDelta = videoWatchMinutes * 0.03 + (chkWillVideo ? 0.1 : 0);
            const readExpDelta = bookReadMinutes * 0.1 + specialReadPages * 0.1;

            return { artExpDelta, intelligenceDelta, knowledgeDelta, staminaDelta, willDelta, charismaDelta, readExpDelta };
        }

        // 计算称号加成（示例逻辑，可根据实际需求修改）
        function computeTitleBonus(attrs) {
            // 假设简单返回 0
            return 0;
        }

        // 计算连续统计（示例逻辑，可根据实际需求修改）
        function computeContinuousCount(history) {
            if (!history.length) return 1;
            const lastTimestamp = history[history.length - 1].timestamp;
            const lastDate = parseDate(lastTimestamp.substr(0, 10));
            const today = new Date(parseDate(formatDate(new Date())));
            const diffDays = Math.floor((today - lastDate) / (1000 * 60 * 60 * 24));
            return diffDays === 1 ? history[history.length - 1].continuousCount + 1 : 1;
        }

        // 更新当日的总体记录
        function updateOverallRecordForDate(dateStr) {
            const existing = data.overall.history.find(r => r.date === dateStr);
            const attrsCopy = { ...attrs };
            let newRecord = {
                date: dateStr,
                artExpCurrent: attrsCopy.artExp.toFixed(2),
                artExpDelta: attrsCopy.artExp.toFixed(2),
                knowledgeCurrent: attrsCopy.knowledge.toFixed(2),
                knowledgeDelta: attrsCopy.knowledge.toFixed(2),
                intelligenceCurrent: attrsCopy.intelligence.toFixed(2),
                intelligenceDelta: attrsCopy.intelligence.toFixed(2),
                dawnSuccess: data.dawn.history.some(r => r.date === dateStr && r.success),
                summaryContent: data.summary.history.find(r => r.date === dateStr)?.content || '',
                taskOverview: data.tasks
                    .filter(t => t.status === 'pending')
                    .map(t => t.name).join(', ')
            };
            if (existing) {
                Object.assign(existing, newRecord);
            } else {
                data.overall.history.push(newRecord);
            }
        }

        // 添加晨曦之约记录
        function addDawnRecord() {
            const today = formatDate(new Date());
            const sleptOnTime = document.getElementById('chkSleptOnTime').checked;
            const wokeOnTime = document.getElementById('chkWokeOnTime').checked;
            const specialCase = document.getElementById('chkSpecialCase').checked;
            const earlySleep = document.getElementById('chkEarlySleep').checked;
            const earlyRise = document.getElementById('chkEarlyRise').checked;
            const success = specialCase ? true : (sleptOnTime && wokeOnTime);
            const willDelta = earlySleep ? 1 : 0;
            const staminaDelta = earlyRise ? 0.5 : 0;
            const rec = { date: today, success, earlySleep, earlyRise, willDelta, staminaDelta };
            data.dawn.history.push(rec);
            updateOverallRecordForDate(today);
            renderAll();
            document.getElementById('dawnForm').reset();
        }

        // 保存每日个人总结
        function addDailySummary() {
            const date = document.getElementById('inputSummaryDate').value;
            const content = document.getElementById('inputSummaryContent').value.trim();
            if (!date) {
                alert('请选择日期');
                return;
            }
            if (data.summary.history.some(r => r.date === date)) {
                alert('该日期已存在总结');
                return;
            }
            data.summary.history.push({ date, content });
            updateOverallRecordForDate(date);
            renderAll();
            document.getElementById('dailySummaryForm').reset();
        }

        // 添加任务
        function addTask() {
            const name = document.getElementById('inputTaskName').value.trim();
            const deadline = document.getElementById('inputTaskDeadline').value;
            const rewards = document.getElementById('inputTaskRewards').value
                .split(',').map(v => parseFloat(v.trim()) || 0);
            const penalties = document.getElementById('inputTaskPenalties').value
                .split(',').map(v => parseFloat(v.trim()) || 0);
            if (!name) {
                alert('请输入任务名称');
                return;
            }
            data.tasks.push({ name, deadline, rewards, penalties, status: 'pending' });
            renderAll();
            document.getElementById('taskForm').reset();
        }

        // 删除记录、任务等
        function deleteDailyRecord(index) {
            if (!confirm('确定删除此条记录？')) return;
            data.daily.history.splice(index, 1);
            renderAll();
        }
        function deleteDawnRecord(index) {
            if (!confirm('确定删除此条晨曦记录？')) return;
            data.dawn.history.splice(index, 1);
            renderAll();
        }
        function deleteDailySummary(index) {
            if (!confirm('确定删除此条总结？')) return;
            data.summary.history.splice(index, 1);
            renderAll();
        }
        function deleteOverallRecord(index) {
            if (!confirm('确定删除此条总记录？')) return;
            data.overall.history.splice(index, 1);
            renderAll();
        }
        function deleteTask(index) {
            if (!confirm('确定删除此任务？')) return;
            data.tasks.splice(index, 1);
            renderAll();
        }

        // 编辑历史记录弹窗：打开
        function openEditRecordModal(index) {
            const rec = data.daily.history[index];
            document.getElementById('editTimestamp').value = rec.timestamp;
            document.getElementById('editStudyMinutes').value = rec.studyMinutes;
            document.getElementById('editArtMinutes').value = rec.artMinutes;
            document.getElementById('editSpecialReadPages').value = rec.specialReadPages;
            document.getElementById('editExerciseMinutes').value = rec.exerciseMinutes;
            document.getElementById('editBookReadMinutes').value = rec.bookReadMinutes;
            document.getElementById('editVideoWatchMinutes').value = rec.videoWatchMinutes;
            document.getElementById('editChkWillStudy').checked = rec.chkWillStudy;
            document.getElementById('editChkWillArt').checked = rec.chkWillArt;
            document.getElementById('editChkWillExercise').checked = rec.chkWillExercise;
            document.getElementById('editChkWillBook').checked = rec.chkWillBook;
            document.getElementById('editChkWillVideo').checked = rec.chkWillVideo;
            document.getElementById('editRecordModal').style.display = 'flex';
            document.getElementById('saveEditRecordBtn').dataset.index = index;
        }

        // 保存编辑后的历史记录
        function saveEditRecord() {
            const index = parseInt(document.getElementById('saveEditRecordBtn').dataset.index, 10);
            const rec = data.daily.history[index];
            rec.studyMinutes = parseInt(document.getElementById('editStudyMinutes').value) || 0;
            rec.artMinutes = parseInt(document.getElementById('editArtMinutes').value) || 0;
            rec.specialReadPages = parseInt(document.getElementById('editSpecialReadPages').value) || 0;
            rec.exerciseMinutes = parseInt(document.getElementById('editExerciseMinutes').value) || 0;
            rec.bookReadMinutes = parseInt(document.getElementById('editBookReadMinutes').value) || 0;
            rec.videoWatchMinutes = parseInt(document.getElementById('editVideoWatchMinutes').value) || 0;
            rec.chkWillStudy = document.getElementById('editChkWillStudy').checked;
            rec.chkWillArt = document.getElementById('editChkWillArt').checked;
            rec.chkWillExercise = document.getElementById('editChkWillExercise').checked;
            rec.chkWillBook = document.getElementById('editChkWillBook').checked;
            rec.chkWillVideo = document.getElementById('editChkWillVideo').checked;

            // 重新计算增量
            const deltas = computeDeltas({
                studyMinutes: rec.studyMinutes,
                artMinutes: rec.artMinutes,
                specialReadPages: rec.specialReadPages,
                exerciseMinutes: rec.exerciseMinutes,
                bookReadMinutes: rec.bookReadMinutes,
                videoWatchMinutes: rec.videoWatchMinutes,
                chkWillStudy: rec.chkWillStudy,
                chkWillArt: rec.chkWillArt,
                chkWillExercise: rec.chkWillExercise,
                chkWillBook: rec.chkWillBook,
                chkWillVideo: rec.chkWillVideo
            });
            rec.deltas.artExpDelta = deltas.artExpDelta;
            rec.deltas.intelligenceDelta = deltas.intelligenceDelta;
            rec.deltas.knowledgeDelta = deltas.knowledgeDelta;
            rec.deltas.staminaDelta = deltas.staminaDelta;
            rec.deltas.willDelta = deltas.willDelta;
            rec.deltas.charismaDelta = deltas.charismaDelta;
            rec.deltas.readExpDelta = deltas.readExpDelta;

            updateOverallRecordForDate(rec.timestamp.substr(0, 10));
            renderAll();
            document.getElementById('editRecordModal').style.display = 'none';
        }

        // 编辑任务弹窗：打开
        function openEditTaskModal(index) {
            const task = data.tasks[index];
            document.getElementById('editTaskName').value = task.name;
            document.getElementById('editTaskDeadline').value = task.deadline;
            document.getElementById('editTaskRewards').value = task.rewards.join(',');
            document.getElementById('editTaskPenalties').value = task.penalties.join(',');
            document.getElementById('editTaskModal').style.display = 'flex';
            document.getElementById('saveEditTaskBtn').dataset.index = index;
        }

        // 保存编辑后的任务
        function saveEditTask() {
            const index = parseInt(document.getElementById('saveEditTaskBtn').dataset.index, 10);
            const task = data.tasks[index];
            task.name = document.getElementById('editTaskName').value.trim();
            task.deadline = document.getElementById('editTaskDeadline').value;
            task.rewards = document.getElementById('editTaskRewards').value
                .split(',').map(v => parseFloat(v.trim()) || 0);
            task.penalties = document.getElementById('editTaskPenalties').value
                .split(',').map(v => parseFloat(v.trim()) || 0);
            renderAll();
            document.getElementById('editTaskModal').style.display = 'none';
        }

        // 点击弹窗外部关闭
        window.addEventListener('click', e => {
            if (e.target === document.getElementById('editRecordModal')) {
                document.getElementById('editRecordModal').style.display = 'none';
            }
            if (e.target === document.getElementById('editTaskModal')) {
                document.getElementById('editTaskModal').style.display = 'none';
            }
        });
    </script>
</body>
</html>
