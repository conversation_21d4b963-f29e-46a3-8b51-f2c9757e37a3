<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>人生游戏计划</title>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body class="bg-gray-100 text-gray-800 font-sans p-4">
  <div class="max-w-7xl mx-auto space-y-4">

    <!-- 顶部信息栏 -->
    <div class="flex flex-col md:flex-row justify-between items-center bg-white p-4 rounded-lg shadow">
      <div>
        <h1 class="text-3xl font-bold mb-2">人生游戏计划</h1>
        <div class="text-sm">当前北京时间：<span id="currentTime"></span></div>
        <div class="text-sm">计划启动天数：<span id="startDays"></span></div>
        <div class="text-sm">启动日期：<input type="date" id="startDate" class="border p-1 rounded" /></div>
      </div>
      <div class="flex space-x-2 mt-2 md:mt-0">
        <button id="saveBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">保存</button>
        <button id="resetBtn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">重置</button>
        <button id="exportBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">导出</button>
        <input type="file" id="importFile" class="hidden" accept=".json" />
        <label for="importFile" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded cursor-pointer">导入</label>
      </div>
    </div>

    <!-- 当前状态面板 -->
    <div class="bg-white p-4 rounded-lg shadow">
      <div class="flex items-center space-x-2">
        <h2 class="text-xl font-semibold">当前状态</h2>
        <button onclick="toggleHelp()" class="text-blue-500 hover:text-blue-700">?</button>
      </div>
      <div id="helpInfo" class="text-sm mt-2 hidden bg-gray-100 p-2 rounded">
        <p>基础属性提升方法、称号获取规则将在后续模块详细列出。</p>
      </div>

      <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- 属性 -->
        <div>
          <h3 class="font-bold mb-2">属性</h3>
          <ul id="attributeList" class="space-y-1 text-sm"></ul>
        </div>

        <!-- 职业 -->
        <div>
          <h3 class="font-bold mb-2">职业水平</h3>
          <ul id="careerList" class="space-y-1 text-sm"></ul>
        </div>

        <!-- 称号 -->
        <div>
          <h3 class="font-bold mb-2">称号</h3>
          <ul id="titleList" class="space-y-1 text-sm"></ul>
        </div>
      </div>
    </div>

    <!-- 后续模块将以标签页形式插入 -->
    <div id="appModules" class="mt-4"></div>
    <!-- 晨曦之约打卡模块 -->
    <div class="bg-white p-4 rounded-lg shadow mt-6">
      <!-- 标签页导航，若已有其它标签可根据需求扩展 -->
      <div class="border-b mb-4">
        <nav class="-mb-px flex space-x-8">
          <a href="#daily" id="tab-daily-alt" class="py-2 px-4 border-b-2 font-medium text-sm border-transparent text-gray-600 hover:text-gray-800 hover:border-gray-300">每日记录</a>
          <a href="#morning" id="tab-morning" class="py-2 px-4 border-b-2 font-medium text-sm border-blue-500 text-blue-600">晨曦之约</a>
        </nav>
      </div>

      <div id="morning" class="tab-content">
        <!-- 打卡表单 -->
        <form id="morningForm" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <label class="inline-flex items-center">
              <input type="checkbox" id="inputSleep" class="form-checkbox">
              <span class="ml-2">及时入睡</span>
            </label>
          </div>
          <div>
            <label class="inline-flex items-center">
              <input type="checkbox" id="inputWake" class="form-checkbox">
              <span class="ml-2">及时起床</span>
            </label>
          </div>
          <div>
            <label class="inline-flex items-center">
              <input type="checkbox" id="inputSpecial" class="form-checkbox">
              <span class="ml-2">特殊情况（免打卡）</span>
            </label>
          </div>
          <div>
            <label class="inline-flex items-center">
              <input type="checkbox" id="inputEarlySleep" class="form-checkbox">
              <span class="ml-2">早睡 +1 意志</span>
            </label>
          </div>
          <div>
            <label class="inline-flex items-center">
              <input type="checkbox" id="inputEarlyRise" class="form-checkbox">
              <span class="ml-2">早起 +0.5 体力</span>
            </label>
          </div>
          <div class="col-span-full text-right">
            <button type="submit" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">打卡</button>
          </div>
        </form>

        <!-- 当日打卡记录 -->
        <div class="mt-6">
          <h3 class="font-semibold mb-2">当日打卡记录</h3>
          <table class="min-w-full bg-white">
            <thead class="bg-gray-100">
              <tr>
                <th class="px-4 py-2 text-left">日期</th>
                <th class="px-4 py-2 text-left">入睡</th>
                <th class="px-4 py-2 text-left">起床</th>
                <th class="px-4 py-2 text-left">结果</th>
                <th class="px-4 py-2 text-left">意志增量</th>
                <th class="px-4 py-2 text-left">体力增量</th>
                <th class="px-4 py-2 text-left">操作</th>
              </tr>
            </thead>
            <tbody id="morningTable" class="divide-y"></tbody>
          </table>
        </div>

        <!-- 历史记录分页 -->
        <div class="mt-4 flex justify-between items-center">
          <button id="morningPrev" class="px-3 py-1 bg-gray-200 rounded">上一页</button>
          <span>第 <span id="morningPage">1</span> 页</span>
          <button id="morningNext" class="px-3 py-1 bg-gray-200 rounded">下一页</button>
        </div>
      </div>
    </div>

    <script>
      // —— 晨曦之约打卡系统 —— 
      if (!gameData.morningRecords) gameData.morningRecords = [];

      const mrPerPage = 10;
      let morningPage = 1;

      // 切换标签（若已有更多标签可改造更健壮的逻辑）
      document.getElementById("tab-daily-alt").onclick = () => {
        document.getElementById("daily").classList.remove("hidden");
        document.getElementById("morning").classList.add("hidden");
        document.getElementById("tab-daily-alt").classList.replace("border-transparent", "border-blue-500");
        document.getElementById("tab-daily-alt").classList.replace("text-gray-600", "text-blue-600");
        document.getElementById("tab-morning").classList.replace("border-blue-500", "border-transparent");
        document.getElementById("tab-morning").classList.replace("text-blue-600", "text-gray-600");
      };
      document.getElementById("tab-morning").onclick = () => {
        document.getElementById("morning").classList.remove("hidden");
        document.getElementById("daily").classList.add("hidden");
        document.getElementById("tab-morning").classList.replace("border-transparent", "border-blue-500");
        document.getElementById("tab-morning").classList.replace("text-gray-600", "text-blue-600");
        document.getElementById("tab-daily-alt").classList.replace("border-blue-500", "border-transparent");
        document.getElementById("tab-daily-alt").classList.replace("text-blue-600", "text-gray-600");
      };

      function calcMorningInc(rec) {
        let incWill = 0, incSta = 0;
        // 判定成功与否
        const success = rec.specialCase || (rec.sleep && rec.wake);
        // 失败惩罚
        if (!success) {
          if (!rec.sleep) incWill -= 1;
          if (!rec.wake) incSta -= 0.5;
        } else {
          // 计算连续成功天数
          const today = rec.date;
          let streak = 1;
          for (let i = gameData.morningRecords.length - 1; i >= 0; i--) {
            const prev = gameData.morningRecords[i];
            if (prev.success && prev.date === new Date(new Date(prev.date).getTime() + 24*3600*1000).toISOString().slice(0,10)) {
              streak++;
            } else break;
          }
          // 奖励
          if (streak >= 30) { incWill += 2; incSta += 1; }
          else if (streak >= 7) { incWill += 1; incSta += 0.5; }
          else if (streak >= 3) { incWill += 0.5; incSta += 0.2; }
        }
        // 特殊选项额外奖励
        if (rec.earlySleep) incWill += 1;
        if (rec.earlyRise)  incSta += 0.5;
        return { willpower: incWill, stamina: incSta, success };
      }

      function addMorning(e) {
        e.preventDefault();
        const date = new Date().toISOString().slice(0,10);
        const rec = {
          date,
          sleep: document.getElementById("inputSleep").checked,
          wake: document.getElementById("inputWake").checked,
          specialCase: document.getElementById("inputSpecial").checked,
          earlySleep: document.getElementById("inputEarlySleep").checked,
          earlyRise: document.getElementById("inputEarlyRise").checked,
        };
        const inc = calcMorningInc(rec);
        rec.inc = inc;
        rec.success = inc.success;
        gameData.morningRecords = [rec, ...gameData.morningRecords];
        // 更新属性
        gameData.attributes.willpower += inc.willpower;
        gameData.attributes.stamina  += inc.stamina;
        saveData();
        updateUI();
        renderMorning();
        e.target.reset();
      }

      function renderMorning() {
        const tb = document.getElementById("morningTable");
        tb.innerHTML = "";
        const start = (morningPage-1)*mrPerPage;
        const slice = gameData.morningRecords.slice(start, start+mrPerPage);
        slice.forEach((rec, idx) => {
          const tr = document.createElement("tr");
          tr.innerHTML = `
            <td class="px-4 py-2">${rec.date}</td>
            <td class="px-4 py-2">${rec.sleep? '✔':'✖'}</td>
            <td class="px-4 py-2">${rec.wake? '✔':'✖'}</td>
            <td class="px-4 py-2">${rec.success? '成功':'失败'}</td>
            <td class="px-4 py-2">${rec.inc.willpower.toFixed(2)}</td>
            <td class="px-4 py-2">${rec.inc.stamina.toFixed(2)}</td>
            <td class="px-4 py-2">
              <button data-idx="${start+idx}" class="delMR text-red-500">删除</button>
            </td>
          `;
          tb.appendChild(tr);
        });
        document.getElementById("morningPage").textContent = morningPage;
        document.querySelectorAll(".delMR").forEach(btn => {
          btn.onclick = () => {
            const i = +btn.dataset.idx;
            if (confirm("确认删除此打卡记录？")) {
              const rec = gameData.morningRecords[i];
              // 回滚
              gameData.attributes.willpower -= rec.inc.willpower;
              gameData.attributes.stamina  -= rec.inc.stamina;
              gameData.morningRecords.splice(i,1);
              saveData(); updateUI(); renderMorning();
            }
          };
        });
      }

      document.getElementById("morningForm").onsubmit = addMorning;
      document.getElementById("morningPrev").onclick = () => {
        if (morningPage>1) { morningPage--; renderMorning(); }
      };
      document.getElementById("morningNext").onclick = () => {
        if (morningPage*mrPerPage < gameData.morningRecords.length) {
          morningPage++; renderMorning();
        }
      };

      // 初始渲染
      renderMorning();
    </script>

  </div>

  <script>
    // 默认属性数据
    const defaultData = {
      startDate: new Date().toISOString().slice(0, 10),
      attributes: {
        intelligence: 0,
        knowledge: 0,
        vision: 0,
        stamina: 0,
        willpower: 0,
        charisma: 0,
        artExp: 1460
      },
      titles: {
        morning: "无",
        will: "无",
        charisma: "无",
        vision: "无"
      },
      records: [],
    };

    let gameData = JSON.parse(localStorage.getItem('lifeGameData')) || defaultData;

    // 更新时间
    function updateTime() {
      const now = new Date();
      document.getElementById("currentTime").textContent = now.toLocaleString("zh-CN", { hour12: false });
      const start = new Date(gameData.startDate);
      const days = Math.floor((now - start) / (1000 * 60 * 60 * 24));
      document.getElementById("startDays").textContent = days + " 天";
    }

    function updateUI() {
      // 设置日期
      document.getElementById("startDate").value = gameData.startDate;

      // 属性列表
      const attrs = gameData.attributes;
      document.getElementById("attributeList").innerHTML = Object.entries(attrs)
        .map(([k, v]) => `<li>${translateKey(k)}：${v.toFixed(2)}</li>`).join("");

      // 职业
      document.getElementById("careerList").innerHTML = `
        <li>幻构师等级：${getArtLevel(attrs.artExp)}</li>
        <li>真理之路 - 知识侧：${getKnowledgeLevel(attrs.knowledge)}</li>
        <li>真理之路 - 智力侧：${getIntelligenceLevel(attrs.intelligence)}</li>
      `;

      // 称号
      const titles = gameData.titles;
      document.getElementById("titleList").innerHTML = `
        <li>晨曦之约：${titles.morning}</li>
        <li>意志称号：${titles.will}</li>
        <li>魅力称号：${titles.charisma}</li>
        <li>阅识称号：${titles.vision}</li>
      `;
    }

    function translateKey(k) {
      return {
        intelligence: "智力",
        knowledge: "知识",
        vision: "阅识",
        stamina: "体力",
        willpower: "意志",
        charisma: "魅力",
        artExp: "幻构师经验"
      }[k] || k;
    }

    function getArtLevel(exp) {
      if (exp < 1500) return "Lv.1 描形学徒";
      if (exp < 4500) return "Lv.2 构素学者";
      if (exp < 9500) return "Lv.3 灵绘使徒";
      if (exp < 17500) return "Lv.4 影纹术士";
      if (exp < 29500) return "Lv.5 心象织者";
      if (exp < 47500) return "Lv.6 空境画匠";
      if (exp < 73500) return "Lv.7 律令绘爵";
      return "Lv.8 幻构师";
    }

    function getKnowledgeLevel(k) {
      if (k < 150) return "Lv.1 灰袍学徒";
      if (k < 650) return "Lv.2 白袍向导";
      if (k < 2150) return "Lv.3 墨衣学者";
      if (k < 6150) return "Lv.4 青衿贤者";
      return "Lv.5 玄冕宗师";
    }

    function getIntelligenceLevel(i) {
      if (i < 150) return "Lv.1 褐衣明理";
      if (i < 650) return "Lv.2 缁衣慎思";
      if (i < 2150) return "Lv.3 朱衣审辩";
      if (i < 6150) return "Lv.4 紫绶格物";
      return "Lv.5 金章弘道";
    }

    function saveData() {
      localStorage.setItem("lifeGameData", JSON.stringify(gameData));
      alert("数据已保存！");
    }

    function resetData() {
      if (confirm("确定要重置所有数据？此操作不可恢复。")) {
        localStorage.removeItem("lifeGameData");
        location.reload();
      }
    }

    function exportData() {
      const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(gameData));
      const a = document.createElement('a');
      a.setAttribute("href", dataStr);
      a.setAttribute("download", "lifeGameData.json");
      a.click();
    }

    document.getElementById("saveBtn").onclick = saveData;
    document.getElementById("resetBtn").onclick = resetData;
    document.getElementById("exportBtn").onclick = exportData;
    document.getElementById("startDate").onchange = (e) => {
      gameData.startDate = e.target.value;
      updateUI();
    };
    document.getElementById("importFile").onchange = (e) => {
      const file = e.target.files[0];
      const reader = new FileReader();
      reader.onload = () => {
        gameData = JSON.parse(reader.result);
        updateUI();
        saveData();
      };
      reader.readAsText(file);
    };

    function toggleHelp() {
      document.getElementById("helpInfo").classList.toggle("hidden");
    }

    updateTime();
    updateUI();
    setInterval(updateTime, 1000);
  </script>
</body>
</html>
