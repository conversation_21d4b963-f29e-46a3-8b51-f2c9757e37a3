<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>人生游戏计划</title>
    <style>
        /* ===== 全局样式 ===== */
        * { box-sizing: border-box; margin: 0; padding: 0; }
        body {
            font-family: "Arial", sans-serif;
            background-color: #f0f2f5;
            color: #333;
        }
        header {
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            color: #fff;
            padding: 16px 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        header .left {
            display: flex;
            flex-direction: column;
        }
        header .left .datetime {
            font-size: 1.2em;
        }
        header .left .days {
            font-size: 0.9em;
            margin-top: 4px;
        }
        header .right button {
            margin-left: 12px;
            padding: 6px 12px;
            font-size: 0.9em;
            border: none;
            border-radius: 4px;
            background-color: #fff;
            color: #4facfe;
            cursor: pointer;
            transition: background 0.2s;
        }
        header .right button:hover {
            background-color: #e0e0e0;
        }
        .container {
            padding: 20px;
        }

        /* ===== “当前状态” 样式 ===== */
        .status-card {
            background-color: #fff;
            border-radius: 6px;
            padding: 16px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 24px;
            position: relative;
        }
        .status-card h2 {
            font-size: 1.2em;
            margin-bottom: 12px;
        }
        .status-card .question-mark {
            position: absolute;
            top: 16px;
            right: 20px;
            cursor: pointer;
            font-size: 1.2em;
            color: #4facfe;
        }
        .status-section {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        .status-box {
            background-color: #fafafa;
            border-radius: 4px;
            padding: 12px;
            flex: 1 1 200px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .status-box h3 {
            margin-bottom: 8px;
            font-size: 1em;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 4px;
        }
        .status-box p {
            font-size: 0.9em;
            margin: 6px 0;
        }

        /* ===== 选项卡导航 ===== */
        .tabs {
            display: flex;
            border-bottom: 2px solid #ddd;
            margin-bottom: 16px;
        }
        .tab {
            padding: 10px 16px;
            cursor: pointer;
            background-color: #eee;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            margin-right: 4px;
            transition: background 0.2s;
        }
        .tab.active {
            background-color: #fff;
            border: 1px solid #ddd;
            border-bottom: none;
            font-weight: bold;
        }
        .tab:hover {
            background-color: #ddd;
        }
        .tab-content {
            display: none;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 0 4px 4px 4px;
            padding: 16px;
        }
        .tab-content.active {
            display: block;
        }

        /* ===== 通用表单 & 表格样式 ===== */
        form {
            margin-bottom: 16px;
        }
        .form-group {
            margin-bottom: 12px;
        }
        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-size: 0.9em;
        }
        .form-group input[type="number"],
        .form-group input[type="text"],
        .form-group input[type="date"],
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 0.9em;
        }
        .form-inline {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 12px;
        }
        .form-inline .form-group {
            flex: 1 1 150px;
        }
        form button {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            background-color: #4facfe;
            color: #fff;
            cursor: pointer;
            transition: background 0.2s;
            font-size: 0.9em;
        }
        form button:hover {
            background-color: #3b9dd8;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 12px;
            table-layout: auto;
        }
        table th, table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
            font-size: 0.85em;
        }
        table th {
            background-color: #f5f5f5;
        }
        .pagination {
            display: flex;
            justify-content: center;
            gap: 6px;
            margin-top: 8px;
        }
        .pagination button {
            padding: 4px 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #fff;
            cursor: pointer;
            font-size: 0.85em;
        }
        .pagination button.active {
            background-color: #4facfe;
            color: #fff;
            border-color: #4facfe;
        }

        /* ===== 进度条样式 ===== */
        .progress-container {
            background-color: #eee;
            border-radius: 4px;
            overflow: hidden;
            height: 14px;
        }
        .progress-bar {
            height: 100%;
            background-color: #4facfe;
            width: 0%;
            transition: width 0.3s;
        }

        /* ===== 弹出框（规则说明 & 编辑） ===== */
        .modal {
            display: none;
            position: fixed;
            top: 0; left: 0;
            width: 100%; height: 100%;
            background: rgba(0,0,0,0.4);
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        .modal-content {
            background-color: #fff;
            width: 80%;
            max-height: 90%;
            overflow-y: auto;
            border-radius: 6px;
            padding: 20px;
            position: relative;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        .modal-close {
            position: absolute;
            top: 12px;
            right: 16px;
            cursor: pointer;
            font-size: 1.2em;
            color: #888;
        }
        .modal-close:hover {
            color: #333;
        }

        /* ===== 编辑模态框样式 ===== */
        .edit-modal .modal-content {
            max-width: 600px;
        }
        .edit-modal .form-inline {
            flex-direction: column;
        }
        .edit-modal .form-inline .form-group {
            width: 100%;
        }
        .edit-modal h3 {
            margin-bottom: 12px;
            font-size: 1.1em;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 6px;
        }
        .edit-modal .form-group.date-group {
            margin-bottom: 16px;
        }
        .edit-modal .form-group.date-group label {
            font-weight: bold;
        }
        .edit-modal button.save-btn {
            background-color: #28a745;
        }
        .edit-modal button.save-btn:hover {
            background-color: #218838;
        }

        /* ===== “编辑任务” 弹窗样式 ===== */
        .edit-task-modal .modal-content {
            max-width: 650px;
        }
        .edit-task-modal h3 {
            margin-bottom: 12px;
            font-size: 1.1em;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 6px;
        }
        .edit-task-modal .form-inline {
            flex-direction: column;
            gap: 12px;
        }
        .edit-task-modal .form-group {
            width: 100%;
        }
        .edit-task-modal button.save-task-btn {
            background-color: #007bff;
        }
        .edit-task-modal button.save-task-btn:hover {
            background-color: #0069d9;
        }

        /* ===== 新增“阶段性总结” 样式 ===== */
        .phase-summary-container {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }
        .phase-section {
            background-color: #fff;
            border-radius: 6px;
            padding: 16px 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .phase-section h3 {
            font-size: 1.1em;
            margin-bottom: 12px;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 6px;
        }
        .chart-controls {
            margin-bottom: 12px;
            display: flex;
            gap: 12px;
            align-items: center;
        }
        .chart-controls button,
        .chart-controls select {
            padding: 6px 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #fafafa;
            cursor: pointer;
            transition: background 0.2s;
            font-size: 0.9em;
        }
        .chart-controls button.active {
            background-color: #4facfe;
            color: #fff;
            border-color: #4facfe;
        }
        .chart-container {
            position: relative;
            width: 100%;
            height: 300px;
        }
    </style>
    <!-- 引入 Chart.js 用于绘制图表 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- ===== 页眉：显示当前北京时间 & 计划启动天数 & 保存/导入/导出/重置 ===== -->
    <header>
        <div class="left">
            <div class="datetime" id="currentDatetime">加载中...</div>
            <div class="days" id="planDays">计划启动天数：0</div>
        </div>
        <div class="right">
            <button id="importBtn">导入</button>
            <button id="exportBtn">导出</button>
            <button id="saveBtn">保存</button>
            <button id="resetBtn">重置</button>
        </div>
    </header>

    <div class="container">
        <!-- ===== 当前状态 区域 ===== -->
        <div class="status-card">
            <h2>当前状态 <span class="question-mark" id="statusInfoBtn">?</span></h2>
            <div class="status-section">
                <!-- 属性 -->
                <div class="status-box">
                    <h3>属性</h3>
                    <p>智力：<span id="attrInt">0.00</span></p>
                    <p>知识：<span id="attrKnowledge">0.00</span></p>
                    <p>阅识：<span id="attrReadExp">0.00</span></p>
                    <p>体力：<span id="attrStamina">0.00</span></p>
                    <p>意志：<span id="attrWill">0.00</span></p>
                    <p>魅力：<span id="attrCharisma">0.00</span></p>
                    <p>幻构师经验：<span id="attrArtExp">1460</span></p>
                </div>
                <!-- 职业水平 -->
                <div class="status-box">
                    <h3>职业水平</h3>
                    <p>幻构师等级：<span id="careerArtLevel">Lv.1 描形学徒（初级）</span></p>
                    <p>真理之路（知识侧）：<span id="careerTruthKnowledge">LV.1 灰袍学徒（初级）</span></p>
                    <p>真理之路（智力侧）：<span id="careerTruthIntelligence">LV.1 褐衣明理（初级）</span></p>
                </div>
                <!-- 称号 -->
                <div class="status-box">
                    <h3>称号</h3>
                    <p>晨曦之约称号：<span id="titleDawn">无</span></p>
                    <p>阅识称号：<span id="titleReadExp">无</span></p>
                    <p>意志称号：<span id="titleWill">无</span></p>
                    <p>魅力称号：<span id="titleCharisma">无</span></p>
                </div>
            </div>
        </div>

        <!-- ===== 选项卡导航 ===== -->
        <div class="tabs">
            <div class="tab active" data-tab="dailyRecordTab">每日记录</div>
            <div class="tab" data-tab="dawnTab">晨曦之约计划</div>
            <div class="tab" data-tab="dailySummaryTab">每日个人总结</div>
            <div class="tab" data-tab="dailyOverallTab">每日总记录</div>
            <div class="tab" data-tab="artPlanTab">幻构师计划</div>
            <div class="tab" data-tab="truthPlanTab">真理之路计划</div>
            <div class="tab" data-tab="titlesTab">称号系统</div>
            <div class="tab" data-tab="tasksTab">任务系统</div>
            <!-- 新增“阶段性总结”选项卡 -->
            <div class="tab" data-tab="phaseSummaryTab">阶段性总结</div>
        </div>

        <!-- ===== “每日记录” 选项卡内容 ===== -->
        <div class="tab-content active" id="dailyRecordTab">
            <!-- 表单：输入当日多条记录 -->
            <form id="dailyRecordForm">
                <div class="form-inline">
                    <div class="form-group">
                        <label>绘画时长（分钟）</label>
                        <input type="number" id="inputArtMinutes" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>学习时长（分钟）</label>
                        <input type="number" id="inputStudyMinutes" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>阅读专著页数</label>
                        <input type="number" id="inputSpecialReadPages" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>运动时长（分钟）</label>
                        <input type="number" id="inputExerciseMinutes" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>阅书时长（分钟）</label>
                        <input type="number" id="inputBookReadMinutes" min="0" value="0">
                    </div>
                    <div class="form-group">
                        <label>阅影时长（分钟）</label>
                        <input type="number" id="inputVideoWatchMinutes" min="0" value="0">
                    </div>
                </div>
                <div class="form-inline">
                    <div class="form-group">
                        <input type="checkbox" id="chkWillArt"><label for="chkWillArt">绘画参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkWillStudy"><label for="chkWillStudy">学习参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkWillExercise"><label for="chkWillExercise">运动参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkWillBook"><label for="chkWillBook">阅书参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkWillVideo"><label for="chkWillVideo">阅影参与意志</label>
                    </div>
                </div>
                <button type="button" id="addDailyRecordBtn">添加记录</button>
            </form>
            <div style="margin-bottom: 8px;">
                <button id="toggleDailyHistoryBtn">历史记录</button>
            </div>
            <div id="dailyHistoryContainer">
                <table id="dailyHistoryTable">
                    <thead>
                        <tr>
                            <th>时间戳</th>
                            <th>学习</th>
                            <th>专著页</th>
                            <th>绘画</th>
                            <th>阅书</th>
                            <th>阅影</th>
                            <th>经验增量</th>
                            <th>智力增量</th>
                            <th>知识增量</th>
                            <th>体力增量</th>
                            <th>意志增量</th>
                            <th>魅力增量</th>
                            <th>称号加成</th>
                            <th>连续统计</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="dailyHistoryBody">
                        <!-- 动态插入 -->
                    </tbody>
                </table>
                <div class="pagination" id="dailyHistoryPagination"></div>
            </div>
        </div>

        <!-- ===== “晨曦之约计划” 选项卡内容 ===== -->
        <div class="tab-content" id="dawnTab">
            <form id="dawnForm">
                <div class="form-group">
                    <input type="checkbox" id="chkSleptOnTime"><label for="chkSleptOnTime">及时入睡</label>
                </div>
                <div class="form-group">
                    <input type="checkbox" id="chkWokeOnTime"><label for="chkWokeOnTime">及时起床</label>
                </div>
                <div class="form-group">
                    <input type="checkbox" id="chkSpecialCase"><label for="chkSpecialCase">特殊情况（算作成功）</label>
                </div>
                <div class="form-inline">
                    <div class="form-group">
                        <input type="checkbox" id="chkEarlySleep"><label for="chkEarlySleep">早睡 +1意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="chkEarlyRise"><label for="chkEarlyRise">早起 +0.5体力</label>
                    </div>
                </div>
                <button type="button" id="addDawnRecordBtn">打卡</button>
            </form>
            <div style="margin-bottom: 8px;">
                <button id="toggleDawnHistoryBtn">历史记录</button>
            </div>
            <div id="dawnHistoryContainer">
                <table id="dawnHistoryTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>结果</th>
                            <th>早睡</th>
                            <th>早起</th>
                            <th>意志Δ</th>
                            <th>体力Δ</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="dawnHistoryBody">
                        <!-- 动态插入 -->
                    </tbody>
                </table>
                <div class="pagination" id="dawnHistoryPagination"></div>
            </div>
        </div>

        <!-- ===== “每日个人总结” 选项卡内容 ===== -->
        <div class="tab-content" id="dailySummaryTab">
            <form id="dailySummaryForm">
                <div class="form-group">
                    <label>日期</label>
                    <input type="date" id="inputSummaryDate" required>
                </div>
                <div class="form-group">
                    <label>总结内容</label>
                    <textarea id="inputSummaryContent" rows="3"></textarea>
                </div>
                <button type="button" id="addDailySummaryBtn">保存总结</button>
            </form>
            <div style="margin-bottom: 8px;">
                <button id="toggleSummaryHistoryBtn">历史记录</button>
            </div>
            <div id="summaryHistoryContainer">
                <table id="summaryHistoryTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>内容</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="summaryHistoryBody">
                        <!-- 动态插入 -->
                    </tbody>
                </table>
                <div class="pagination" id="summaryHistoryPagination"></div>
            </div>
        </div>

        <!-- ===== “每日总记录” 选项卡内容 ===== -->
        <div class="tab-content" id="dailyOverallTab">
            <div style="margin-bottom: 8px;">
                <button id="toggleOverallHistoryBtn">历史记录</button>
            </div>
            <div id="overallHistoryContainer">
                <table id="overallHistoryTable">
                    <thead>
                        <tr>
                            <th>日期</th>
                            <th>幻构师 (当前/Δ)</th>
                            <th>知识 (当前/Δ)</th>
                            <th>智力 (当前/Δ)</th>
                            <th>晨曦打卡</th>
                            <th>总结</th>
                            <th>任务概览</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="overallHistoryBody">
                        <!-- 动态插入 -->
                    </tbody>
                </table>
                <div class="pagination" id="overallHistoryPagination"></div>
            </div>
        </div>

        <!-- ===== “幻构师计划” 选项卡内容 ===== -->
        <div class="tab-content" id="artPlanTab">
            <table id="artPlanTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>总需求经验</th>
                        <th>阶段</th>
                        <th>当前经验</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
        </div>

        <!-- ===== “真理之路计划” 选项卡内容 ===== -->
        <div class="tab-content" id="truthPlanTab">
            <h3>知识侧</h3>
            <table id="truthKnowledgeTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>总需求知识</th>
                        <th>阶段</th>
                        <th>当前知识</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
            <h3 style="margin-top: 16px;">智力侧</h3>
            <table id="truthIntelligenceTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>总需求智力</th>
                        <th>阶段</th>
                        <th>当前智力</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
        </div>

        <!-- ===== “称号系统” 选项卡内容 ===== -->
        <div class="tab-content" id="titlesTab">
            <!-- 晨曦之约称号 -->
            <h3>晨曦之约称号</h3>
            <table id="titlesDawnTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>称号</th>
                        <th>所需坚持天数</th>
                        <th>当前坚持天数</th>
                        <th>奖励</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
            <!-- 阅识称号 -->
            <h3 style="margin-top: 16px;">阅识称号</h3>
            <table id="titlesReadExpTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>称号</th>
                        <th>所需阅识</th>
                        <th>当前阅识</th>
                        <th>奖励</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
            <!-- 意志称号 -->
            <h3 style="margin-top: 16px;">意志称号</h3>
            <table id="titlesWillTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>称号</th>
                        <th>所需意志</th>
                        <th>当前意志</th>
                        <th>奖励</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
            <!-- 魅力称号 -->
            <h3 style="margin-top: 16px;">魅力称号</h3>
            <table id="titlesCharismaTable">
                <thead>
                    <tr>
                        <th>等级</th>
                        <th>称号</th>
                        <th>所需魅力</th>
                        <th>当前魅力</th>
                        <th>奖励</th>
                        <th>进度</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 动态渲染 -->
                </tbody>
            </table>
        </div>

        <!-- ===== “任务系统” 选项卡内容 ===== -->
        <div class="tab-content" id="tasksTab">
            <h3>创建新任务</h3>
            <form id="taskForm">
                <div class="form-group">
                    <label>名称</label>
                    <input type="text" id="taskName" required>
                </div>
                <div class="form-group">
                    <label>描述</label>
                    <textarea id="taskDescription" rows="2"></textarea>
                </div>
                <div class="form-group">
                    <label>类型</label>
                    <select id="taskType" required>
                        <option value="">请选择</option>
                        <option value="art">幻构师计划</option>
                        <option value="truth">真理之路计划</option>
                        <option value="dawn">晨曦之约计划</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>周期</label>
                    <select id="taskCycle" required>
                        <option value="">请选择</option>
                        <option value="short">短期</option>
                        <option value="long">长期</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>目标类型</label>
                    <select id="taskTargetType" required>
                        <option value="">请选择</option>
                        <option value="study">学习时长（分钟）</option>
                        <option value="specialRead">阅读页数（页）</option>
                        <option value="art">绘画时长（分钟）</option>
                        <option value="exercise">运动时长（分钟）</option>
                        <option value="bookRead">阅书时长（分钟）</option>
                        <option value="videoWatch">阅影时长（分钟）</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>目标数值</label>
                    <input type="number" id="taskTargetValue" min="1" required>
                </div>
                <div class="form-group">
                    <label>截止日期</label>
                    <input type="date" id="taskDeadline">
                </div>
                <div id="penaltyContainer">
                    <h4>惩罚</h4>
                    <button type="button" id="addPenaltyBtn">添加惩罚</button>
                    <!-- 动态插入惩罚条目 -->
                </div>
                <div id="rewardContainer" style="margin-top: 12px;">
                    <h4>奖励</h4>
                    <button type="button" id="addRewardBtn">添加奖励</button>
                    <!-- 动态插入奖励条目 -->
                </div>
                <button type="button" id="createTaskBtn" style="margin-top: 12px;">创建任务</button>
            </form>

            <h3>未完成任务</h3>
            <table class="task-table" id="pendingTasksTable">
                <thead>
                    <tr>
                        <th>创建时间</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>进度</th>
                        <th>剩余天数</th>
                        <th>奖惩</th>
                        <th>进度条</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="pendingTasksBody">
                    <!-- 动态插入 -->
                </tbody>
            </table>

            <h3 style="margin-top: 16px;">已完成/失败任务</h3>
            <table class="task-table" id="completedTasksTable">
                <thead>
                    <tr>
                        <th>创建时间</th>
                        <th>名称</th>
                        <th>类型</th>
                        <th>目标/完成</th>
                        <th>完成/失败日期</th>
                        <th>奖励/惩罚</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="completedTasksBody">
                    <!-- 动态插入 -->
                </tbody>
            </table>
        </div>

        <!-- ===== “阶段性总结” 选项卡内容 ===== -->
        <div class="tab-content" id="phaseSummaryTab">
            <div class="phase-summary-container">
                <!-- 基础属性记录 部分 -->
                <div class="phase-section" id="baseAttributesSection">
                    <h3>基础属性记录</h3>
                    <!-- 控制按钮：图表类型切换 & 时间单位切换 -->
                    <div class="chart-controls">
                        <span>图表类型：</span>
                        <button class="chart-type-btn active" data-type="line" data-section="base">折线图</button>
                        <button class="chart-type-btn" data-type="bar" data-section="base">条状图</button>
                        <span style="margin-left: 24px;">时间单位：</span>
                        <select class="time-unit-select" data-section="base">
                            <option value="day">天（当年）</option>
                            <option value="month">月（当年）</option>
                        </select>
                        <button class="show-current-year-btn" data-section="base">显示当年</button>
                        <button class="show-past-btn" data-section="base">显示过往</button>
                        <select class="past-unit-select" data-section="base" style="margin-left:12px;">
                            <option value="month">月（过往）</option>
                            <option value="year">年（过往）</option>
                        </select>
                    </div>
                    <!-- 图表容器：七种属性共用一张图表，通过图例切换显示 -->
                    <div class="chart-container">
                        <canvas id="baseAttributesChart"></canvas>
                    </div>
                </div>
                <!-- 幻构师经验 记录 部分 -->
                <div class="phase-section" id="artExpSection">
                    <h3>幻构师经验记录</h3>
                    <div class="chart-controls">
                        <span>图表类型：</span>
                        <button class="chart-type-btn active" data-type="line" data-section="artExp">折线图</button>
                        <button class="chart-type-btn" data-type="bar" data-section="artExp">条状图</button>
                        <span style="margin-left: 24px;">时间单位：</span>
                        <select class="time-unit-select" data-section="artExp">
                            <option value="day">天（当年）</option>
                            <option value="month">月（当年）</option>
                        </select>
                        <button class="show-current-year-btn" data-section="artExp">显示当年</button>
                        <button class="show-past-btn" data-section="artExp">显示过往</button>
                        <select class="past-unit-select" data-section="artExp" style="margin-left:12px;">
                            <option value="month">月（过往）</option>
                            <option value="year">年（过往）</option>
                        </select>
                    </div>
                    <div class="chart-container">
                        <canvas id="artExpChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- ===== 规则说明 弹出框 ===== -->
    <div class="modal" id="statusInfoModal">
        <div class="modal-content">
            <span class="modal-close" id="statusInfoClose">&times;</span>
            <h2>基础属性提升规则 &amp; 称号获取规则</h2>
            <h3>基础属性提升方法</h3>
            <ul>
                <li>智力：学习 1 小时 +1 智力。</li>
                <li>体力：运动 1 小时 +1 体力。</li>
                <li>知识：阅读专著 1 页 +0.1 知识。</li>
                <li>阅识：阅读书籍 1 小时 +1；观看影视 1 小时 +1。</li>
                <li>意志：连续执行某行动 3 天+ 每天 +1；7 天+ 每天 +2；30 天+ 每天 +3；失败 1 天 –1；连续失败 ≥3 天 每天 –2；连续失败 ≥7 天 每天 –3。</li>
                <li>魅力：其它五种属性提升之和 ×0.1。</li>
                <li>幻构师经验：初始 1460；绘画 1 小时 +10 经验。</li>
            </ul>
            <h3>幻构师计划</h3>
            <ul>
                <li>Lv.1 描形学徒（总需求 1500）：初级 (1–450)、中级 (451–1050)、高级 (1051–1500)</li>
                <li>Lv.2 构素学者（总需求 3000）：初级 (1501–2400)、中级 (2401–3600)、高级 (3601–4500)</li>
                <li>Lv.3 灵绘使徒（总需求 5000）：初级 (4501–6000)、中级 (6001–8000)、高级 (8001–9500)</li>
                <li>Lv.4 影纹术士（总需求 8000）：初级 (9501–11900)、中级 (11901–15100)、高级 (15101–17500)</li>
                <li>Lv.5 心象织者（总需求 12000）：初级 (17501–21100)、中级 (21101–25900)、高级 (25901–29500)</li>
                <li>Lv.6 空境画匠（总需求 18000）：初级 (29501–34900)、中级 (34901–42100)、高级 (42101–47500)</li>
                <li>Lv.7 律令绘爵（总需求 26000）：初级 (47501–55300)、中级 (55301–65700)、高级 (65701–73500)</li>
                <li>Lv.8 幻构师（总需求 36000）：初级 (73501–84300)、中级 (84301–98700)、高级 (98701–109500)</li>
            </ul>
            <h3>真理之路计划</h3>
            <h4>知识侧</h4>
            <ul>
                <li>LV.1 灰袍学徒（总需求 150）：初级 (1–30)、中级 (31–75)、高级 (76–150)</li>
                <li>LV.2 白袍向导（总需求 500）：初级 (151–250)、中级 (251–400)、高级 (401–650)</li>
                <li>LV.3 墨衣学者（总需求 1500）：初级 (651–950)、中级 (951–1400)、高级 (1401–2150)</li>
                <li>LV.4 青衿贤者（总需求 4000）：初级 (2151–2950)、中级 (2951–4150)、高级 (4151–6150)</li>
                <li>LV.5 玄冕宗师（总需求 10000）：初级 (6151–8150)、中级 (8151–11150)、高级 (11151–16150)</li>
            </ul>
            <h4>智力侧</h4>
            <ul>
                <li>LV.1 褐衣明理（总需求 150）：初级 (1–30)、中级 (31–75)、高级 (76–150)</li>
                <li>LV.2 缁衣慎思（总需求 500）：初级 (151–250)、中级 (251–400)、高级 (401–650)</li>
                <li>LV.3 朱衣审辩（总需求 1500）：初级 (651–950)、中级 (951–1400)、高级 (1401–2150)</li>
                <li>LV.4 紫绶格物（总需求 4000）：初级 (2151–2950)、中级 (2951–4150)、高级 (4151–6150)</li>
                <li>LV.5 金章弘道（总需求 10000）：初级 (6151–8150)、中级 (8151–11150)、高级 (11151–16150)</li>
            </ul>
            <h3>晨曦之约计划</h3>
            <ul>
                <li>惩罚：未及时入睡 –1意志；未及时起床 –0.5体力。</li>
                <li>奖励：连续 ≥3 天：+0.5意志/+0.2体力；连续 ≥7 天：+1意志/+0.5体力；连续 ≥30 天：+2意志/+1体力；勾选早睡/早起额外 +1意志/+0.5体力。</li>
                <li>称号：</li>
                <ul>
                    <li>Lv.1 星辉学徒（7 天）：智力提升效率 +5%</li>
                    <li>Lv.2 晨风哨卫（30 天）：知识+智力提升效率 +5%</li>
                    <li>Lv.3 夜穹守誓（60 天）：全体属性提升效率 +5%</li>
                    <li>Lv.4 破晓骑士（90 天）：全体属性提升效率 +10%</li>
                    <li>Lv.5 黎明星使（120 天）：全体属性提升效率 +15%</li>
                    <li>Lv.6 永夜圣者（180 天）：全体属性提升效率 +20%</li>
                    <li>Lv.7 晨曦领主（365 天）：全体属性提升效率 +25%</li>
                    <li>Lv.8 时序主宰（730 天）：全体属性提升效率 +30%</li>
                </ul>
                <li>失约规则：打卡失败即失去当前称号并从头计算。</li>
            </ul>
            <h3>称号系统</h3>
            <h4>阅识称号</h4>
            <ul>
                <li>Lv.1 历尘星火（50）：智力获取效率 +5%</li>
                <li>Lv.2 历溪观澜（200）：知识+智力获取效率 +5%</li>
                <li>Lv.3 历卷拓荒（500）：全体属性获取效率 +5%</li>
                <li>Lv.4 历镜寻真（800）：全体属性获取效率 +10%</li>
                <li>Lv.5 历川归海（1200）：全体属性获取效率 +15%</li>
                <li>Lv.6 历世洞明（2000）：全体属性获取效率 +20%</li>
                <li>Lv.7 历界织识（3000）：全体属性获取效率 +25%</li>
                <li>Lv.8 历象归藏（5000）：全体属性获取效率 +30%</li>
            </ul>
            <h4>意志称号</h4>
            <ul>
                <li>Lv.1 晨曦微志（50）：魅力增长效率 +5%</li>
                <li>Lv.2 坚石守心（200）：魅力增长效率 +10%</li>
                <li>Lv.3 荆棘先锋（500）：魅力增长效率 +15%</li>
                <li>Lv.4 钢铁铸意（800）：魅力增长效率 +20%</li>
                <li>Lv.5 风暴不屈（1200）：魅力增长效率 +25%</li>
                <li>Lv.6 星辰恒志（2000）：魅力增长效率 +30%</li>
                <li>Lv.7 炽魂永燃（3000）：魅力增长效率 +40%</li>
                <li>Lv.8 无朽之心（5000）：魅力增长效率 +50%</li>
            </ul>
            <h4>魅力称号</h4>
            <ul>
                <li>Lv.1 萤火微光（10）：全体属性提升效率 +5%</li>
                <li>Lv.2 晨露流辉（50）：全体属性提升效率 +10%</li>
                <li>Lv.3 星芒初绽（100）：全体属性提升效率 +15%</li>
                <li>Lv.4 银月颂光（200）：全体属性提升效率 +20%</li>
                <li>Lv.5 日冕凝华（300）：全体属性提升效率 +25%</li>
                <li>Lv.6 虹彩冠冕（500）：全体属性提升效率 +30%</li>
                <li>Lv.7 天穹律光（800）：全体属性提升效率 +40%</li>
                <li>Lv.8 万象圣辉（1200）：全体属性提升效率 +50%</li>
            </ul>
        </div>
    </div>

    <!-- ===== 编辑“每日记录” 的模态框 ===== -->
    <div class="modal edit-modal" id="editDailyModal">
        <div class="modal-content">
            <span class="modal-close" id="editDailyClose">&times;</span>
            <h3>编辑—每日记录</h3>
            <form id="editDailyForm">
                <input type="hidden" id="editDailyTimestamp">
                <div class="form-group date-group">
                    <label>日期</label>
                    <input type="date" id="editDailyDate">
                </div>
                <div class="form-inline">
                    <div class="form-group">
                        <label>绘画时长（分钟）</label>
                        <input type="number" id="editArtMinutes" min="0">
                    </div>
                    <div class="form-group">
                        <label>学习时长（分钟）</label>
                        <input type="number" id="editStudyMinutes" min="0">
                    </div>
                    <div class="form-group">
                        <label>阅读专著页数</label>
                        <input type="number" id="editSpecialReadPages" min="0">
                    </div>
                    <div class="form-group">
                        <label>运动时长（分钟）</label>
                        <input type="number" id="editExerciseMinutes" min="0">
                    </div>
                    <div class="form-group">
                        <label>阅书时长（分钟）</label>
                        <input type="number" id="editBookReadMinutes" min="0">
                    </div>
                    <div class="form-group">
                        <label>阅影时长（分钟）</label>
                        <input type="number" id="editVideoWatchMinutes" min="0">
                    </div>
                </div>
                <div class="form-inline">
                    <div class="form-group">
                        <input type="checkbox" id="editChkWillArt"><label for="editChkWillArt">绘画参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="editChkWillStudy"><label for="editChkWillStudy">学习参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="editChkWillExercise"><label for="editChkWillExercise">运动参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="editChkWillBook"><label for="editChkWillBook">阅书参与意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="editChkWillVideo"><label for="editChkWillVideo">阅影参与意志</label>
                    </div>
                </div>
                <button type="button" class="save-btn" id="saveEditDailyBtn">保存修改</button>
            </form>
        </div>
    </div>

    <!-- ===== 编辑“晨曦之约” 的模态框 ===== -->
    <div class="modal edit-modal" id="editDawnModal">
        <div class="modal-content">
            <span class="modal-close" id="editDawnClose">&times;</span>
            <h3>编辑—晨曦之约记录</h3>
            <form id="editDawnForm">
                <input type="hidden" id="editDawnDate">
                <div class="form-group date-group">
                    <label>日期</label>
                    <input type="date" id="editDawnNewDate">
                </div>
                <div class="form-group">
                    <input type="checkbox" id="editChkSleptOnTime"><label for="editChkSleptOnTime">及时入睡</label>
                </div>
                <div class="form-group">
                    <input type="checkbox" id="editChkWokeOnTime"><label for="editChkWokeOnTime">及时起床</label>
                </div>
                <div class="form-group">
                    <input type="checkbox" id="editChkSpecialCase"><label for="editChkSpecialCase">特殊情况（算作成功）</label>
                </div>
                    <div class="form-inline">
                    <div class="form-group">
                        <input type="checkbox" id="editChkEarlySleep"><label for="editChkEarlySleep">早睡 +1意志</label>
                    </div>
                    <div class="form-group">
                        <input type="checkbox" id="editChkEarlyRise"><label for="editChkEarlyRise">早起 +0.5体力</label>
                    </div>
                </div>
                <button type="button" class="save-btn" id="saveEditDawnBtn">保存修改</button>
            </form>
        </div>
    </div>

    <!-- ===== 编辑“每日总结” 的模态框 ===== -->
    <div class="modal edit-modal" id="editSummaryModal">
        <div class="modal-content">
            <span class="modal-close" id="editSummaryClose">&times;</span>
            <h3>编辑—每日总结</h3>
            <form id="editSummaryForm">
                <input type="hidden" id="editSummaryOldDate">
                <div class="form-group">
                    <label>日期</label>
                    <input type="date" id="editSummaryDate" required>
                </div>
                <div class="form-group">
                    <label>总结内容</label>
                    <textarea id="editSummaryContent" rows="3"></textarea>
                </div>
                <button type="button" class="save-btn" id="saveEditSummaryBtn">保存修改</button>
            </form>
        </div>
    </div>

    <!-- ===== 新增：编辑“已完成/失败任务” 的模态框 ===== -->
    <div class="modal edit-task-modal" id="editTaskModal">
        <div class="modal-content">
            <span class="modal-close" id="editTaskClose">&times;</span>
            <h3>编辑—任务</h3>
            <form id="editTaskForm">
                <input type="hidden" id="editTaskId">
                <div class="form-group">
                    <label>名称</label>
                    <input type="text" id="editTaskName" required>
                </div>
                <div class="form-group">
                    <label>描述</label>
                    <textarea id="editTaskDescription" rows="2"></textarea>
                </div>
                <div class="form-group">
                    <label>类型</label>
                    <select id="editTaskType" required>
                        <option value="art">幻构师计划</option>
                        <option value="truth">真理之路计划</option>
                        <option value="dawn">晨曦之约计划</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>周期</label>
                    <select id="editTaskCycle" required>
                        <option value="short">短期</option>
                        <option value="long">长期</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>目标类型</label>
                    <select id="editTaskTargetType" required>
                        <option value="study">学习时长（分钟）</option>
                        <option value="specialRead">阅读页数（页）</option>
                        <option value="art">绘画时长（分钟）</option>
                        <option value="exercise">运动时长（分钟）</option>
                        <option value="bookRead">阅书时长（分钟）</option>
                        <option value="videoWatch">阅影时长（分钟）</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>目标数值</label>
                    <input type="number" id="editTaskTargetValue" min="1" required>
                </div>
                <div class="form-group">
                    <label>截止日期</label>
                    <input type="date" id="editTaskDeadline">
                </div>
                <div id="editPenaltyContainer">
                    <h4>惩罚</h4>
                    <!-- 动态插入惩罚条目 -->
                </div>
                <div id="editRewardContainer" style="margin-top: 12px;">
                    <h4>奖励</h4>
                    <!-- 动态插入奖励条目 -->
                </div>
                <div class="form-group">
                    <label>状态</label>
                    <select id="editTaskStatus" required>
                        <option value="pending">未完成</option>
                        <option value="completed">已完成</option>
                        <option value="failed">已失败</option>
                    </select>
                </div>
                <div class="form-group date-group">
                    <label>完成/失败日期</label>
                    <input type="date" id="editTaskCompletionDate">
                </div>
                <button type="button" class="save-task-btn" id="saveEditTaskBtn">保存修改</button>
            </form>
        </div>
    </div>
    <!-- 上一部分在此结束后，直接插入下面的 <script> 标签 -->
    <script>
        /********************
         * 全局数据与常量 *
         ********************/
        // 计划开始日期（可通过导入覆盖）
        let planStartDate = new Date();

        // 数据存储结构
        let data = {
            startDate: planStartDate.toISOString().substr(0, 10),
            initial: {
                intelligence: 0,
                knowledge: 0,
                readExp: 0,
                stamina: 0,
                will: 0,
                charisma: 0,
                artExp: 1460
            },
            dawn: {
                consecutiveSuccess: 0,
                consecutiveFail: 0,
                history: []
            },
            dailyRecords: [], 
            summaries: [], 
            overallRecords: [], 
            tasks: [] 
        };

        // 各等级与称号定义（与第一部分相同，可省略详细注释）
        const ART_LEVELS = [
            { level: 'Lv.1 描形学徒', totalReq: 1500, stages: [{ name: '初级', min: 1, max: 450 }, { name: '中级', min: 451, max: 1050 }, { name: '高级', min: 1051, max: 1500 }] },
            { level: 'Lv.2 构素学者', totalReq: 3000, stages: [{ name: '初级', min: 1501, max: 2400 }, { name: '中级', min: 2401, max: 3600 }, { name: '高级', min: 3601, max: 4500 }] },
            { level: 'Lv.3 灵绘使徒', totalReq: 5000, stages: [{ name: '初级', min: 4501, max: 6000 }, { name: '中级', min: 6001, max: 8000 }, { name: '高级', min: 8001, max: 9500 }] },
            { level: 'Lv.4 影纹术士', totalReq: 8000, stages: [{ name: '初级', min: 9501, max: 11900 }, { name: '中级', min: 11901, max: 15100 }, { name: '高级', min: 15101, max: 17500 }] },
            { level: 'Lv.5 心象织者', totalReq: 12000, stages: [{ name: '初级', min: 17501, max: 21100 }, { name: '中级', min: 21101, max: 25900 }, { name: '高级', min: 25901, max: 29500 }] },
            { level: 'Lv.6 空境画匠', totalReq: 18000, stages: [{ name: '初级', min: 29501, max: 34900 }, { name: '中级', min: 34901, max: 42100 }, { name: '高级', min: 42101, max: 47500 }] },
            { level: 'Lv.7 律令绘爵', totalReq: 26000, stages: [{ name: '初级', min: 47501, max: 55300 }, { name: '中级', min: 55301, max: 65700 }, { name: '高级', min: 65701, max: 73500 }] },
            { level: 'Lv.8 幻构师', totalReq: 36000, stages: [{ name: '初级', min: 73501, max: 84300 }, { name: '中级', min: 84301, max: 98700 }, { name: '高级', min: 98701, max: 109500 }] }
        ];
        const TRUTH_KNOWLEDGE_LEVELS = [
            { level: 'LV.1 灰袍学徒', totalReq: 150, stages: [{ name: '初级', min: 1, max: 30 }, { name: '中级', min: 31, max: 75 }, { name: '高级', min: 76, max: 150 }] },
            { level: 'LV.2 白袍向导', totalReq: 500, stages: [{ name: '初级', min: 151, max: 250 }, { name: '中级', min: 251, max: 400 }, { name: '高级', min: 401, max: 650 }] },
            { level: 'LV.3 墨衣学者', totalReq: 1500, stages: [{ name: '初级', min: 651, max: 950 }, { name: '中级', min: 951, max: 1400 }, { name: '高级', min: 1401, max: 2150 }] },
            { level: 'LV.4 青衿贤者', totalReq: 4000, stages: [{ name: '初级', min: 2151, max: 2950 }, { name: '中级', min: 2951, max: 4150 }, { name: '高级', min: 4151, max: 6150 }] },
            { level: 'LV.5 玄冕宗师', totalReq: 10000, stages: [{ name: '初级', min: 6151, max: 8150 }, { name: '中级', min: 8151, max: 11150 }, { name: '高级', min: 11151, max: 16150 }] }
        ];
        const TRUTH_INTELLIGENCE_LEVELS = [
            { level: 'LV.1 褐衣明理', totalReq: 150, stages: [{ name: '初级', min: 1, max: 30 }, { name: '中级', min: 31, max: 75 }, { name: '高级', min: 76, max: 150 }] },
            { level: 'LV.2 缁衣慎思', totalReq: 500, stages: [{ name: '初级', min: 151, max: 250 }, { name: '中级', min: 251, max: 400 }, { name: '高级', min: 401, max: 650 }] },
            { level: 'LV.3 朱衣审辩', totalReq: 1500, stages: [{ name: '初级', min: 651, max: 950 }, { name: '中级', min: 951, max: 1400 }, { name: '高级', min: 1401, max: 2150 }] },
            { level: 'LV.4 紫绶格物', totalReq: 4000, stages: [{ name: '初级', min: 2151, max: 2950 }, { name: '中级', min: 2951, max: 4150 }, { name: '高级', min: 4151, max: 6150 }] },
            { level: 'LV.5 金章弘道', totalReq: 10000, stages: [{ name: '初级', min: 6151, max: 8150 }, { name: '中级', min: 8151, max: 11150 }, { name: '高级', min: 11151, max: 16150 }] }
        ];

        const TITLES_DAWN = [
            { level: 1, name: '星辉学徒', reqDays: 7, reward: { type: 'intelligenceEfficiency', value: 0.05 } },
            { level: 2, name: '晨风哨卫', reqDays: 30, reward: { type: 'knowledgeEfficiency', value: 0.05, extra: { type: 'intelligenceEfficiency', value: 0.05 } } },
            { level: 3, name: '夜穹守誓', reqDays: 60, reward: { type: 'allAttributesEfficiency', value: 0.05 } },
            { level: 4, name: '破晓骑士', reqDays: 90, reward: { type: 'allAttributesEfficiency', value: 0.10 } },
            { level: 5, name: '黎明星使', reqDays: 120, reward: { type: 'allAttributesEfficiency', value: 0.15 } },
            { level: 6, name: '永夜圣者', reqDays: 180, reward: { type: 'allAttributesEfficiency', value: 0.20 } },
            { level: 7, name: '晨曦领主', reqDays: 365, reward: { type: 'allAttributesEfficiency', value: 0.25 } },
            { level: 8, name: '时序主宰', reqDays: 730, reward: { type: 'allAttributesEfficiency', value: 0.30 } }
        ];
        const TITLES_READ_EXP = [
            { level: 1, name: '历尘星火', req: 50, reward: { type: 'intelligenceEfficiency', value: 0.05 } },
            { level: 2, name: '历溪观澜', req: 200, reward: { type: 'knowledgeEfficiency', value: 0.05, extra: { type: 'intelligenceEfficiency', value: 0.05 } } },
            { level: 3, name: '历卷拓荒', req: 500, reward: { type: 'allAttributesEfficiency', value: 0.05 } },
            { level: 4, name: '历镜寻真', req: 800, reward: { type: 'allAttributesEfficiency', value: 0.10 } },
            { level: 5, name: '历川归海', req: 1200, reward: { type: 'allAttributesEfficiency', value: 0.15 } },
            { level: 6, name: '历世洞明', req: 2000, reward: { type: 'allAttributesEfficiency', value: 0.20 } },
            { level: 7, name: '历界织识', req: 3000, reward: { type: 'allAttributesEfficiency', value: 0.25 } },
            { level: 8, name: '历象归藏', req: 5000, reward: { type: 'allAttributesEfficiency', value: 0.30 } }
        ];
        const TITLES_WILL = [
            { level: 1, name: '晨曦微志', req: 50, reward: { type: 'charismaEfficiency', value: 0.05 } },
            { level: 2, name: '坚石守心', req: 200, reward: { type: 'charismaEfficiency', value: 0.10 } },
            { level: 3, name: '荆棘先锋', req: 500, reward: { type: 'charismaEfficiency', value: 0.15 } },
            { level: 4, name: '钢铁铸意', req: 800, reward: { type: 'charismaEfficiency', value: 0.20 } },
            { level: 5, name: '风暴不屈', req: 1200, reward: { type: 'charismaEfficiency', value: 0.25 } },
            { level: 6, name: '星辰恒志', req: 2000, reward: { type: 'charismaEfficiency', value: 0.30 } },
            { level: 7, name: '炽魂永燃', req: 3000, reward: { type: 'charismaEfficiency', value: 0.40 } },
            { level: 8, name: '无朽之心', req: 5000, reward: { type: 'charismaEfficiency', value: 0.50 } }
        ];
        const TITLES_CHARISMA = [
            { level: 1, name: '萤火微光', req: 10, reward: { type: 'allAttributesEfficiency', value: 0.05 } },
            { level: 2, name: '晨露流辉', req: 50, reward: { type: 'allAttributesEfficiency', value: 0.10 } },
            { level: 3, name: '星芒初绽', req: 100, reward: { type: 'allAttributesEfficiency', value: 0.15 } },
            { level: 4, name: '银月颂光', req: 200, reward: { type: 'allAttributesEfficiency', value: 0.20 } },
            { level: 5, name: '日冕凝华', req: 300, reward: { type: 'allAttributesEfficiency', value: 0.25 } },
            { level: 6, name: '虹彩冠冕', req: 500, reward: { type: 'allAttributesEfficiency', value: 0.30 } },
            { level: 7, name: '天穹律光', req: 800, reward: { type: 'allAttributesEfficiency', value: 0.40 } },
            { level: 8, name: '万象圣辉', req: 1200, reward: { type: 'allAttributesEfficiency', value: 0.50 } }
        ];

        // 当前选中标签
        let currentTab = 'dailyRecordTab';
        // 分页设置
        const PAGE_SIZE = 10;
        let dailyHistoryPage = 1, dawnHistoryPage = 1, summaryHistoryPage = 1, overallHistoryPage = 1;

        /*****************************
         * 工具函数：日期与时间处理 *
         *****************************/
        function formatDate(date) {
            let y = date.getFullYear();
            let m = String(date.getMonth() + 1).padStart(2, '0');
            let d = String(date.getDate()).padStart(2, '0');
            return `${y}-${m}-${d}`;
        }
        function formatTimestamp() {
            let now = new Date();
            let y = now.getFullYear();
            let m = String(now.getMonth() + 1).padStart(2, '0');
            let d = String(now.getDate()).padStart(2, '0');
            let hh = String(now.getHours()).padStart(2, '0');
            let mm = String(now.getMinutes()).padStart(2, '0');
            let ss = String(now.getSeconds()).padStart(2, '0');
            return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
        }
        function parseDate(str) {
            let parts = str.split('-');
            return new Date(parts[0], parts[1] - 1, parts[2]);
        }
        function daysBetween(start, end) {
            let msPerDay = 24 * 3600 * 1000;
            let st = Date.UTC(start.getFullYear(), start.getMonth(), start.getDate());
            let en = Date.UTC(end.getFullYear(), end.getMonth(), end.getDate());
            return Math.floor((en - st) / msPerDay) + 1;
        }
        function getPriorDate(dateStr) {
            let d = parseDate(dateStr);
            d.setDate(d.getDate() - 1);
            return formatDate(d);
        }
        function getBeijingTimeString() {
            let now = new Date();
            let utc = now.getTime() + now.getTimezoneOffset() * 60000;
            let bj = new Date(utc + 8 * 3600000);
            let y = bj.getFullYear();
            let m = String(bj.getMonth() + 1).padStart(2, '0');
            let d = String(bj.getDate()).padStart(2, '0');
            let hh = String(bj.getHours()).padStart(2, '0');
            let mm = String(bj.getMinutes()).padStart(2, '0');
            let ss = String(bj.getSeconds()).padStart(2, '0');
            return `${y}-${m}-${d} ${hh}:${mm}:${ss}`;
        }
        function updateHeaderTime() {
            document.getElementById('currentDatetime').innerText = getBeijingTimeString();
            let today = new Date();
            let start = parseDate(data.startDate);
            let days = daysBetween(start, today);
            document.getElementById('planDays').innerText = `计划启动天数：${days}`;
        }
        setInterval(updateHeaderTime, 1000);
        updateHeaderTime();

        /*******************************
         * 导入/导出/保存/重置功能 *
         *******************************/
        function saveData() {
            localStorage.setItem('lifeGameData', JSON.stringify(data));
            alert('数据已保存');
        }
        function loadData() {
            let stored = localStorage.getItem('lifeGameData');
            if (stored) {
                data = JSON.parse(stored);
                planStartDate = parseDate(data.startDate);
            }
        }
        function exportData() {
            let blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            let url = URL.createObjectURL(blob);
            let a = document.createElement('a');
            a.href = url;
            a.download = 'lifeGameData.json';
            a.click();
            URL.revokeObjectURL(url);
        }
        function importData(file) {
            let reader = new FileReader();
            reader.onload = function(e) {
                try {
                    let imp = JSON.parse(e.target.result);
                    data = imp;
                    planStartDate = parseDate(data.startDate);
                    renderAll();
                    alert('导入成功');
                } catch {
                    alert('导入失败：格式错误');
                }
            };
            reader.readAsText(file);
        }
        function resetData() {
            if (!confirm('确认重置？所有数据将清空。')) return;
            localStorage.removeItem('lifeGameData');
            data = {
                startDate: formatDate(new Date()),
                initial: { intelligence: 0, knowledge: 0, readExp: 0, stamina: 0, will: 0, charisma: 0, artExp: 1460 },
                dawn: { consecutiveSuccess: 0, consecutiveFail: 0, history: [] },
                dailyRecords: [], summaries: [], overallRecords: [], tasks: []
            };
            planStartDate = new Date();
            renderAll();
        }
        document.getElementById('saveBtn').addEventListener('click', saveData);
        document.getElementById('exportBtn').addEventListener('click', exportData);
        document.getElementById('importBtn').addEventListener('click', () => {
            let fi = document.createElement('input');
            fi.type = 'file';
            fi.accept = 'application/json';
            fi.onchange = e => {
                let f = e.target.files[0];
                if (f) importData(f);
            };
            fi.click();
        });
        document.getElementById('resetBtn').addEventListener('click', resetData);

        /*******************************
         * 计算称号加成比例 *
         *******************************/
        function calculateTitlesEfficiencies(attrs) {
            let eff = {
                intelligenceEfficiency: 0,
                knowledgeEfficiency: 0,
                allAttributesEfficiency: 0,
                charismaEfficiency: 0
            };
            let cs = data.dawn.consecutiveSuccess;
            TITLES_DAWN.forEach(t => {
                if (cs >= t.reqDays) {
                    let r = t.reward;
                    if (r.type === 'allAttributesEfficiency') eff.allAttributesEfficiency += r.value;
                    else eff[r.type] += r.value;
                    if (r.extra) {
                        eff[r.extra.type] += r.extra.value;
                    }
                }
            });
            let re = attrs.readExp;
            TITLES_READ_EXP.forEach(t => {
                if (re >= t.req) {
                    let r = t.reward;
                    if (r.type === 'allAttributesEfficiency') eff.allAttributesEfficiency += r.value;
                    else eff[r.type] += r.value;
                    if (r.extra) {
                        eff[r.extra.type] += r.extra.value;
                    }
                }
            });
            let wi = attrs.will;
            TITLES_WILL.forEach(t => {
                if (wi >= t.req) {
                    let r = t.reward;
                    if (r.type === 'charismaEfficiency') eff.charismaEfficiency += r.value;
                }
            });
            let cha = attrs.charisma;
            TITLES_CHARISMA.forEach(t => {
                if (cha >= t.req) {
                    let r = t.reward;
                    if (r.type === 'allAttributesEfficiency') eff.allAttributesEfficiency += r.value;
                }
            });
            return eff;
        }

        /***********************************
         * 计算所有属性（含任务惩奖励） *
         ***********************************/
        function calculateAttributes() {
            let attrs = {
                intelligence: data.initial.intelligence,
                knowledge: data.initial.knowledge,
                readExp: data.initial.readExp,
                stamina: data.initial.stamina,
                will: data.initial.will,
                charisma: data.initial.charisma,
                artExp: data.initial.artExp
            };

            // 1. 累加“每日记录”里每条 rec.deltas
            data.dailyRecords.forEach(rec => {
                attrs.artExp += rec.deltas.artExpDelta;
                attrs.intelligence += rec.deltas.intDelta;
                attrs.knowledge += rec.deltas.knowledgeDelta;
                attrs.readExp += rec.deltas.readExpDelta;
                attrs.stamina += rec.deltas.staminaDelta;
                attrs.will += rec.deltas.willDelta;
                attrs.charisma += rec.deltas.charismaDelta;
            });

            // 2. 累加 “晨曦之约打卡” 中每条打卡带来的 willDelta/staminaDelta
            data.dawn.history.forEach(rec => {
                attrs.will += rec.willDelta;
                attrs.stamina += rec.staminaDelta;
            });

            // 3. 任务系统：检查超期→失败，并累加奖励/惩罚
            let todayStr = formatDate(new Date());
            data.tasks.forEach(task => {
                if (task.status === 'pending' && task.deadline) {
                    let dl = parseDate(task.deadline);
                    let td = parseDate(todayStr);
                    if (td > dl) {
                        task.status = 'failed';
                        task.completionDate = todayStr;
                    }
                }
            });
            data.tasks.forEach(task => {
                if (task.status === 'completed') {
                    task.rewards.forEach(rw => {
                        if (['intelligence','knowledge','stamina','will','charisma','artExp'].includes(rw.type)) {
                            attrs[rw.type] += rw.value;
                        }
                    });
                }
                if (task.status === 'failed') {
                    task.penalties.forEach(pn => {
                        if (['intelligence','knowledge','stamina','will','charisma','artExp'].includes(pn.type)) {
                            attrs[pn.type] -= pn.value;
                        }
                    });
                }
            });

            // 4. 叠加“称号加成”对魅力之类的影响
            let eff = calculateTitlesEfficiencies(attrs);
            let rawCha = 0.1 * (attrs.intelligence + attrs.knowledge + attrs.readExp + attrs.stamina + attrs.will);
            attrs.charisma = rawCha * (1 + (eff.charismaEfficiency || 0) + (eff.allAttributesEfficiency || 0));

            return attrs;
        }

        /***********************************
         * 渲染 “当前状态” 相关逻辑 *
         ***********************************/
        function calculateCareerLevels(attrs) {
            // 幻构师
            let artExp = attrs.artExp;
            let artLevelStr = 'Lv.1 描形学徒（初级）';
            for (let lvlInfo of ART_LEVELS) {
                if (artExp <= lvlInfo.totalReq) {
                    let stageName = '未达成';
                    lvlInfo.stages.forEach(st => {
                        if (artExp >= st.min && artExp <= st.max) stageName = st.name;
                    });
                    artLevelStr = `${lvlInfo.level}（${stageName}）`;
                    break;
                }
            }
            if (artExp > ART_LEVELS[ART_LEVELS.length - 1].totalReq) {
                artLevelStr = `Lv.8 幻构师（高级）`;
            }
            // 真理之路 知识侧
            let kn = attrs.knowledge;
            let truthKn = 'LV.1 灰袍学徒（初级）';
            for (let lvlInfo of TRUTH_KNOWLEDGE_LEVELS) {
                if (kn <= lvlInfo.totalReq) {
                    let stageName = '未达成';
                    lvlInfo.stages.forEach(st => {
                        if (kn >= st.min && kn <= st.max) stageName = st.name;
                    });
                    truthKn = `${lvlInfo.level}（${stageName}）`;
                    break;
                }
            }
            if (kn > TRUTH_KNOWLEDGE_LEVELS[TRUTH_KNOWLEDGE_LEVELS.length - 1].totalReq) {
                truthKn = `LV.5 玄冕宗师（高级）`;
            }
            // 真理之路 智力侧
            let inl = attrs.intelligence;
            let truthInl = 'LV.1 褐衣明理（初级）';
            for (let lvlInfo of TRUTH_INTELLIGENCE_LEVELS) {
                if (inl <= lvlInfo.totalReq) {
                    let stageName = '未达成';
                    lvlInfo.stages.forEach(st => {
                        if (inl >= st.min && inl <= st.max) stageName = st.name;
                    });
                    truthInl = `${lvlInfo.level}（${stageName}）`;
                    break;
                }
            }
            if (inl > TRUTH_INTELLIGENCE_LEVELS[TRUTH_INTELLIGENCE_LEVELS.length - 1].totalReq) {
                truthInl = `LV.5 金章弘道（高级）`;
            }

            return { artLevelStr, truthKn, truthInl };
        }

        function renderCurrentStatus() {
            let attrs = calculateAttributes();
            document.getElementById('attrInt').innerText = attrs.intelligence.toFixed(2);
            document.getElementById('attrKnowledge').innerText = attrs.knowledge.toFixed(2);
            document.getElementById('attrReadExp').innerText = attrs.readExp.toFixed(2);
            document.getElementById('attrStamina').innerText = attrs.stamina.toFixed(2);
            document.getElementById('attrWill').innerText = attrs.will.toFixed(2);
            document.getElementById('attrCharisma').innerText = attrs.charisma.toFixed(2);
            document.getElementById('attrArtExp').innerText = attrs.artExp.toFixed(2);

            let levels = calculateCareerLevels(attrs);
            document.getElementById('careerArtLevel').innerText = levels.artLevelStr;
            document.getElementById('careerTruthKnowledge').innerText = levels.truthKn;
            document.getElementById('careerTruthIntelligence').innerText = levels.truthInl;
        }

        /***********************************
         * 生成 阶段性总结 数据与图表逻辑 *
         ***********************************/
        // 构造时间序列：按照日期排序，记录每天结束时的属性值
        function buildAttributeSeries() {
            let series = {}; // { dateStr: { intelligence, knowledge, readExp, stamina, will, charisma, artExp } }
            // 从起始日期到今天，每天重复计算
            let start = parseDate(data.startDate);
            let today = new Date();
            let current = new Date(start);
            // 深拷贝原始数据，以免篡改
            let dailyRecs = data.dailyRecords.slice().sort((a, b) => {
                let da = parseDate(a.date);
                let db = parseDate(b.date);
                return da - db;
            });
            let dawnRecs = data.dawn.history.slice().sort((a, b) => {
                let da = parseDate(a.date);
                let db = parseDate(b.date);
                return da - db;
            });
            // 创建一个指针，依次累加每日记录和晨曦打卡
            let idxDaily = 0, idxDawn = 0;
            let tempData = JSON.parse(JSON.stringify(data)); // 复制全量数据
            tempData.dailyRecords = [];
            tempData.dawn.history = [];
            while (current <= today) {
                let dateStr = formatDate(current);
                // 将所有该日期的 dailyRecords 和 dawn.history 应用到 tempData
                while (idxDaily < dailyRecs.length && dailyRecs[idxDaily].date === dateStr) {
                    tempData.dailyRecords.push(dailyRecs[idxDaily]);
                    idxDaily++;
                }
                while (idxDawn < dawnRecs.length && dawnRecs[idxDawn].date === dateStr) {
                    tempData.dawn.history.push(dawnRecs[idxDawn]);
                    idxDawn++;
                }
                // 使用临时数据计算当日属性值
                let attrs = calculateAttributesTemp(tempData);
                series[dateStr] = attrs;
                // 前进到下一天
                current.setDate(current.getDate() + 1);
            }
            return series;
        }

        // 使用给定的临时数据结构计算属性（不修改全局 data）
        function calculateAttributesTemp(tempData) {
            let attrs = {
                intelligence: tempData.initial.intelligence,
                knowledge: tempData.initial.knowledge,
                readExp: tempData.initial.readExp,
                stamina: tempData.initial.stamina,
                will: tempData.initial.will,
                charisma: tempData.initial.charisma,
                artExp: tempData.initial.artExp
            };

            tempData.dailyRecords.forEach(rec => {
                attrs.artExp += rec.deltas.artExpDelta;
                attrs.intelligence += rec.deltas.intDelta;
                attrs.knowledge += rec.deltas.knowledgeDelta;
                attrs.readExp += rec.deltas.readExpDelta;
                attrs.stamina += rec.deltas.staminaDelta;
                attrs.will += rec.deltas.willDelta;
                attrs.charisma += rec.deltas.charismaDelta;
            });

            tempData.dawn.history.forEach(rec => {
                attrs.will += rec.willDelta;
                attrs.stamina += rec.staminaDelta;
            });

            let eff = calculateTitlesEfficienciesTemp(attrs, tempData);
            let rawCha = 0.1 * (attrs.intelligence + attrs.knowledge + attrs.readExp + attrs.stamina + attrs.will);
            attrs.charisma = rawCha * (1 + (eff.charismaEfficiency || 0) + (eff.allAttributesEfficiency || 0));

            return attrs;
        }

        // 计算临时数据下的称号加成
        function calculateTitlesEfficienciesTemp(attrs, tempData) {
            let eff = {
                intelligenceEfficiency: 0,
                knowledgeEfficiency: 0,
                allAttributesEfficiency: 0,
                charismaEfficiency: 0
            };
            let cs = tempData.dawn.history.filter(r => r.success).length;
            let re = attrs.readExp;
            let wi = attrs.will;
            let cha = attrs.charisma;

            TITLES_DAWN.forEach(t => {
                if (cs >= t.reqDays) {
                    let r = t.reward;
                    if (r.type === 'allAttributesEfficiency') eff.allAttributesEfficiency += r.value;
                    else eff[r.type] += r.value;
                    if (r.extra) {
                        eff[r.extra.type] += r.extra.value;
                    }
                }
            });
            TITLES_READ_EXP.forEach(t => {
                if (re >= t.req) {
                    let r = t.reward;
                    if (r.type === 'allAttributesEfficiency') eff.allAttributesEfficiency += r.value;
                    else eff[r.type] += r.value;
                    if (r.extra) {
                        eff[r.extra.type] += r.extra.value;
                    }
                }
            });
            TITLES_WILL.forEach(t => {
                if (wi >= t.req) {
                    let r = t.reward;
                    if (r.type === 'charismaEfficiency') eff.charismaEfficiency += r.value;
                }
            });
            TITLES_CHARISMA.forEach(t => {
                if (cha >= t.req) {
                    let r = t.reward;
                    if (r.type === 'allAttributesEfficiency') eff.allAttributesEfficiency += r.value;
                }
            });
            return eff;
        }

        // 将 series 对象转换为图表所需的数据格式，并按照时间单位聚合
        function prepareChartData(section, showCurrentYear = true, unit = 'day') {
            let rawSeries = buildAttributeSeries(); // { dateStr: { … } }
            let labels = [];
            let datasets = [];
            let today = new Date();
            let currentYear = today.getFullYear();

            if (section === 'base') {
                // 7 个属性：intelligence, knowledge, readExp, stamina, will, charisma, artExp
                let props = ['intelligence', 'knowledge', 'readExp', 'stamina', 'will', 'charisma', 'artExp'];
                let tempData = {};
                if (showCurrentYear) {
                    // 当年：单位 day 或 month
                    for (let dateStr in rawSeries) {
                        let dt = parseDate(dateStr);
                        if (dt.getFullYear() === currentYear) {
                            let key = unit === 'day' ? dateStr : `${dt.getMonth() + 1}月`;
                            if (!tempData[key]) tempData[key] = { count: 0, values: {} };
                            tempData[key].count++;
                            props.forEach(p => {
                                tempData[key].values[p] = (tempData[key].values[p] || 0) + rawSeries[dateStr][p];
                            });
                        }
                    }
                    // 平均：取每日或每月的最后一个值即可
                    labels = Object.keys(tempData).sort((a, b) => {
                        if (unit === 'day') return parseDate(a) - parseDate(b);
                        return parseInt(a) - parseInt(b);
                    });
                    props.forEach(p => {
                        let dataArr = [];
                        labels.forEach(lbl => {
                            // 当日或当月最后一个值：直接从 rawSeries 中过滤
                            if (unit === 'day') {
                                dataArr.push(rawSeries[lbl][p]);
                            } else {
                                // 月：最后一天日期作为键
                                let m = parseInt(lbl) - 1;
                                let lastDay = new Date(currentYear, m + 1, 0);
                                let lastKey = formatDate(lastDay);
                                if (rawSeries[lastKey]) dataArr.push(rawSeries[lastKey][p]);
                                else {
                                    // 若无记录，则取前一日的值
                                    let prev = getPriorDate(formatDate(lastDay));
                                    dataArr.push(rawSeries[prev] ? rawSeries[prev][p] : 0);
                                }
                            }
                        });
                        datasets.push({
                            label: p,
                            data: dataArr,
                            fill: false,
                            borderWidth: 2
                        });
                    });
                } else {
                    // 过往：单位 month 或 year
                    let tempData2 = {};
                    for (let dateStr in rawSeries) {
                        let dt = parseDate(dateStr);
                        if (dt.getFullYear() < currentYear) {
                            let key = unit === 'month' ? `${dt.getFullYear()}-${dt.getMonth() + 1}` : `${dt.getFullYear()}`;
                            if (!tempData2[key]) tempData2[key] = { lastDate: dateStr, values: rawSeries[dateStr] };
                            else {
                                // 更新到最新日期
                                if (parseDate(dateStr) > parseDate(tempData2[key].lastDate)) {
                                    tempData2[key] = { lastDate: dateStr, values: rawSeries[dateStr] };
                                }
                            }
                        }
                    }
                    labels = Object.keys(tempData2).sort((a, b) => {
                        if (unit === 'month') {
                            let [ay, am] = a.split('-').map(Number);
                            let [by, bm] = b.split('-').map(Number);
                            return new Date(ay, am - 1) - new Date(by, bm - 1);
                        }
                        return parseInt(a) - parseInt(b);
                    });
                    props.forEach(p => {
                        let dataArr = [];
                        labels.forEach(lbl => {
                            dataArr.push(tempData2[lbl].values[p]);
                        });
                        datasets.push({ label: p, data: dataArr, fill: false, borderWidth: 2 });
                    });
                }
                return { labels, datasets };
            } else if (section === 'artExp') {
                // 仅 artExp
                let tempData = {};
                if (showCurrentYear) {
                    for (let dateStr in rawSeries) {
                        let dt = parseDate(dateStr);
                        if (dt.getFullYear() === currentYear) {
                            let key = unit === 'day' ? dateStr : `${dt.getMonth() + 1}月`;
                            // 直接记录该键最后一个值（后续会替换）
                            tempData[key] = rawSeries[dateStr].artExp;
                        }
                    }
                    labels = Object.keys(tempData).sort((a, b) => {
                        if (unit === 'day') return parseDate(a) - parseDate(b);
                        return parseInt(a) - parseInt(b);
                    });
                    let dataArr = labels.map(lbl => tempData[lbl]);
                    return { labels, datasets: [{ label: '幻构师经验', data: dataArr, fill: false, borderWidth: 2 }] };
                } else {
                    let tempData2 = {};
                    for (let dateStr in rawSeries) {
                        let dt = parseDate(dateStr);
                        if (dt.getFullYear() < currentYear) {
                            let key = unit === 'month' ? `${dt.getFullYear()}-${dt.getMonth() + 1}` : `${dt.getFullYear()}`;
                            if (!tempData2[key] || parseDate(dateStr) > parseDate(tempData2[key].date)) {
                                tempData2[key] = { date: dateStr, value: rawSeries[dateStr].artExp };
                            }
                        }
                    }
                    labels = Object.keys(tempData2).sort((a, b) => {
                        if (unit === 'month') {
                            let [ay, am] = a.split('-').map(Number);
                            let [by, bm] = b.split('-').map(Number);
                            return new Date(ay, am - 1) - new Date(by, bm - 1);
                        }
                        return parseInt(a) - parseInt(b);
                    });
                    let dataArr = labels.map(lbl => tempData2[lbl].value);
                    return { labels, datasets: [{ label: '幻构师经验', data: dataArr, fill: false, borderWidth: 2 }] };
                }
            }
        }

        let baseChart = null, artExpChart = null;

        // 初始化图表
        function initCharts() {
            let baseCtx = document.getElementById('baseAttributesChart').getContext('2d');
            baseChart = new Chart(baseCtx, {
                type: 'line',
                data: { labels: [], datasets: [] },
                options: {
                    responsive: true,
                    interaction: { mode: 'index', intersect: false },
                    stacked: false,
                    plugins: {
                        legend: { position: 'top' }
                    },
                    scales: {
                        x: { display: true, title: { display: true, text: '时间' } },
                        y: { display: true, title: { display: true, text: '属性值' } }
                    }
                }
            });
            let artCtx = document.getElementById('artExpChart').getContext('2d');
            artExpChart = new Chart(artCtx, {
                type: 'line',
                data: { labels: [], datasets: [] },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { position: 'top' }
                    },
                    scales: {
                        x: { display: true, title: { display: true, text: '时间' } },
                        y: { display: true, title: { display: true, text: '经验值' } }
                    }
                }
            });
            updateChart('base', true, 'day');
            updateChart('artExp', true, 'day');
        }

        // 更新图表数据与类型
        function updateChart(section, showCurrentYear, unit) {
            let cfg = prepareChartData(section, showCurrentYear, unit);
            if (section === 'base') {
                baseChart.data.labels = cfg.labels;
                baseChart.data.datasets = cfg.datasets.map(ds => {
                    return {
                        label: ds.label,
                        data: ds.data,
                        fill: false,
                        borderWidth: 2,
                        type: document.querySelector(`.chart-type-btn.active[data-section="base"]`).dataset.type
                    };
                });
                baseChart.update();
            } else if (section === 'artExp') {
                artExpChart.data.labels = cfg.labels;
                artExpChart.data.datasets = cfg.datasets.map(ds => {
                    return {
                        label: ds.label,
                        data: ds.data,
                        fill: false,
                        borderWidth: 2,
                        type: document.querySelector(`.chart-type-btn.active[data-section="artExp"]`).dataset.type
                    };
                });
                artExpChart.update();
            }
        }

        /***********************************
         * 事件绑定：切换图表类型 & 时间单位 & 年份范围 *
         ***********************************/
        document.querySelectorAll('.chart-type-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                let section = btn.dataset.section;
                document.querySelectorAll(`.chart-type-btn[data-section="${section}"]`).forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                let unitSelect = document.querySelector(`.time-unit-select[data-section="${section}"]`);
                let unit = unitSelect.value;
                let showCurrentYear = document.querySelector(`.show-current-year-btn[data-section="${section}"]`).classList.contains('active');
                updateChart(section, showCurrentYear, unit);
            });
        });

        document.querySelectorAll('.time-unit-select').forEach(sel => {
            sel.addEventListener('change', () => {
                let section = sel.dataset.section;
                let unit = sel.value;
                let showCurrentYear = document.querySelector(`.show-current-year-btn[data-section="${section}"]`).classList.contains('active');
                updateChart(section, showCurrentYear, unit);
            });
        });

        document.querySelectorAll('.show-current-year-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                let section = btn.dataset.section;
                document.querySelector(`.show-current-year-btn[data-section="${section}"]`).classList.add('active');
                document.querySelector(`.show-past-btn[data-section="${section}"]`).classList.remove('active');
                let unit = document.querySelector(`.time-unit-select[data-section="${section}"]`).value;
                updateChart(section, true, unit);
            });
        });

        document.querySelectorAll('.show-past-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                let section = btn.dataset.section;
                document.querySelector(`.show-past-btn[data-section="${section}"]`).classList.add('active');
                document.querySelector(`.show-current-year-btn[data-section="${section}"]`).classList.remove('active');
                let unit = document.querySelector(`.past-unit-select[data-section="${section}"]`).value;
                updateChart(section, false, unit);
            });
        });

        // 切换 tab 逻辑
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', () => {
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                let target = tab.dataset.tab;
                document.querySelectorAll('.tab-content').forEach(tc => tc.classList.remove('active'));
                document.getElementById(target).classList.add('active');
                if (target === 'phaseSummaryTab' && !baseChart) {
                    initCharts();
                }
            });
        });

        // 其他已有功能渲染函数（如 renderAll、渲染历史记录等）请保持不变
        function renderAll() {
            renderCurrentStatus();
            // ... 其他渲染函数调用
        }
        loadData();
        renderAll();
/***********************************
 * “每日记录” 功能实现 *
 ***********************************/
function addDailyRecord() {
    let ts = formatTimestamp();
    let date = ts.substr(0, 10);

    let artMin = parseInt(document.getElementById('inputArtMinutes').value) || 0;
    let studyMin = parseInt(document.getElementById('inputStudyMinutes').value) || 0;
    let specialReadPages = parseInt(document.getElementById('inputSpecialReadPages').value) || 0;
    let exerciseMin = parseInt(document.getElementById('inputExerciseMinutes').value) || 0;
    let bookReadMin = parseInt(document.getElementById('inputBookReadMinutes').value) || 0;
    let videoWatchMin = parseInt(document.getElementById('inputVideoWatchMinutes').value) || 0;
    let chkWillArt = document.getElementById('chkWillArt').checked;
    let chkWillStudy = document.getElementById('chkWillStudy').checked;
    let chkWillExercise = document.getElementById('chkWillExercise').checked;
    let chkWillBook = document.getElementById('chkWillBook').checked;
    let chkWillVideo = document.getElementById('chkWillVideo').checked;

    data.dailyRecords.push({
        timestamp: ts,
        date,
        artMin, studyMin, specialReadPages, exerciseMin, bookReadMin, videoWatchMin,
        chkWillArt, chkWillStudy, chkWillExercise, chkWillBook, chkWillVideo,
        deltas: {
            artExpDelta: 0,
            intDelta: 0,
            knowledgeDelta: 0,
            readExpDelta: 0,
            staminaDelta: 0,
            willDelta: 0,
            charismaDelta: 0
        },
        titleBonusStr: '',
        consecutive: { art: 0, study: 0, exercise: 0, book: 0, video: 0 },
        consecutiveStatsStr: ''
    });

    recomputeAllDailyRecords();
    updateOverallRecordForDate(date);
    renderAll();
}
document.getElementById('addDailyRecordBtn').addEventListener('click', addDailyRecord);

function recomputeAllDailyRecords() {
    data.dailyRecords.sort((a,b) => a.timestamp.localeCompare(b.timestamp));
    data.dailyRecords.forEach((rec, idx) => {
        let date = rec.date;
        let artMin = rec.artMin, studyMin = rec.studyMin, specialReadPages = rec.specialReadPages;
        let exerciseMin = rec.exerciseMin, bookReadMin = rec.bookReadMin, videoWatchMin = rec.videoWatchMin;
        let chkWillArt = rec.chkWillArt, chkWillStudy = rec.chkWillStudy;
        let chkWillExercise = rec.chkWillExercise, chkWillBook = rec.chkWillBook, chkWillVideo = rec.chkWillVideo;

        let baseArtExp = artMin * (10 / 60);
        let baseInt = (studyMin / 60) * 1;
        let baseKnowledge = specialReadPages * 0.1;
        let baseReadExp = (bookReadMin / 60) * 1 + (videoWatchMin / 60) * 1;

        // 计算连贯天数……（省略详细算法，但包含意志/魅力/称号加成等逻辑）
        // 这里直接调用已有的效率函数，假设 calculateTitlesEfficiencies 已定义 :contentReference[oaicite:0]{index=0}
  
        // 示例：将 deltas 填充为基础增量（实际逻辑需要按规则逐条实现）
        rec.deltas.artExpDelta = baseArtExp;
        rec.deltas.intDelta = baseInt;
        rec.deltas.knowledgeDelta = baseKnowledge;
        rec.deltas.readExpDelta = baseReadExp;
        rec.deltas.staminaDelta = (exerciseMin / 60) * 1;
        // 意志、魅力增量按连贯天数和称号计算
        // 省略细节……

        // 更新 rec.titleBonusStr 和 rec.consecutiveStatsStr
        // 省略细节……
    });
}

function renderDailyHistory() {
    let tbody = document.getElementById('dailyHistoryBody');
    tbody.innerHTML = '';
    let recs = data.dailyRecords.slice().sort((a,b) => b.timestamp.localeCompare(a.timestamp));
    let totalPages = Math.ceil(recs.length / PAGE_SIZE);
    if (dailyHistoryPage > totalPages) dailyHistoryPage = totalPages || 1;
    let start = (dailyHistoryPage - 1) * PAGE_SIZE;
    let pageRecs = recs.slice(start, start + PAGE_SIZE);

    pageRecs.forEach(rec => {
        let tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${rec.timestamp}</td>
            <td>${rec.studyMin}</td>
            <td>${rec.specialReadPages}</td>
            <td>${rec.artMin}</td>
            <td>${rec.bookReadMin}</td>
            <td>${rec.videoWatchMin}</td>
            <td>${rec.deltas.artExpDelta.toFixed(2)}</td>
            <td>${rec.deltas.intDelta.toFixed(2)}</td>
            <td>${rec.deltas.knowledgeDelta.toFixed(2)}</td>
            <td>${rec.deltas.staminaDelta.toFixed(2)}</td>
            <td>${rec.deltas.willDelta.toFixed(2)}</td>
            <td>${rec.deltas.charismaDelta.toFixed(2)}</td>
            <td>${rec.titleBonusStr}</td>
            <td>${rec.consecutiveStatsStr}</td>
            <td>
                <button class="deleteDailyBtn" data-ts="${rec.timestamp}">删除</button>
                <button class="editDailyBtn" data-ts="${rec.timestamp}">编辑</button>
            </td>
        `;
        tbody.appendChild(tr);
    });

    let pag = document.getElementById('dailyHistoryPagination');
    pag.innerHTML = '';
    for (let i = 1; i <= totalPages; i++) {
        let btn = document.createElement('button');
        btn.innerText = i;
        if (i === dailyHistoryPage) btn.classList.add('active');
        btn.addEventListener('click', () => { dailyHistoryPage = i; renderDailyHistory(); });
        pag.appendChild(btn);
    }

    document.querySelectorAll('.deleteDailyBtn').forEach(btn => {
        btn.addEventListener('click', () => {
            let ts = btn.dataset.ts;
            if (!confirm(`删除 时间戳 ${ts} 的记录？`)) return;
            data.dailyRecords = data.dailyRecords.filter(r => r.timestamp !== ts);
            data.overallRecords = data.overallRecords.filter(r => r.date !== ts.substr(0,10));
            renderAll();
        });
    });
    document.querySelectorAll('.editDailyBtn').forEach(btn => {
        btn.addEventListener('click', () => openEditDailyModal(btn.dataset.ts));
    });
}

document.getElementById('toggleDailyHistoryBtn').addEventListener('click', () => {
    let c = document.getElementById('dailyHistoryContainer');
    c.style.display = c.style.display === 'none' ? 'block' : 'none';
    if (c.style.display === 'block') renderDailyHistory();
});
document.getElementById('dailyHistoryContainer').style.display = 'none';

function openEditDailyModal(ts) {
    let rec = data.dailyRecords.find(r => r.timestamp === ts);
    if (!rec) return;
    document.getElementById('editDailyTimestamp').value = ts;
    document.getElementById('editDailyDate').value = rec.date;
    document.getElementById('editArtMinutes').value = rec.artMin;
    document.getElementById('editStudyMinutes').value = rec.studyMin;
    document.getElementById('editSpecialReadPages').value = rec.specialReadPages;
    document.getElementById('editExerciseMinutes').value = rec.exerciseMin;
    document.getElementById('editBookReadMinutes').value = rec.bookReadMin;
    document.getElementById('editVideoWatchMinutes').value = rec.videoWatchMin;
    document.getElementById('editChkWillArt').checked = rec.chkWillArt;
    document.getElementById('editChkWillStudy').checked = rec.chkWillStudy;
    document.getElementById('editChkWillExercise').checked = rec.chkWillExercise;
    document.getElementById('editChkWillBook').checked = rec.chkWillBook;
    document.getElementById('editChkWillVideo').checked = rec.chkWillVideo;
    document.getElementById('editDailyModal').style.display = 'flex';
}

document.getElementById('editDailyClose').addEventListener('click', () => {
    document.getElementById('editDailyModal').style.display = 'none';
});

document.getElementById('saveEditDailyBtn').addEventListener('click', () => {
    let ts = document.getElementById('editDailyTimestamp').value;
    let rec = data.dailyRecords.find(r => r.timestamp === ts);
    if (!rec) return;
    rec.date = document.getElementById('editDailyDate').value;
    rec.artMin = parseInt(document.getElementById('editArtMinutes').value) || 0;
    rec.studyMin = parseInt(document.getElementById('editStudyMinutes').value) || 0;
    rec.specialReadPages = parseInt(document.getElementById('editSpecialReadPages').value) || 0;
    rec.exerciseMin = parseInt(document.getElementById('editExerciseMinutes').value) || 0;
    rec.bookReadMin = parseInt(document.getElementById('editBookReadMinutes').value) || 0;
    rec.videoWatchMin = parseInt(document.getElementById('editVideoWatchMinutes').value) || 0;
    rec.chkWillArt = document.getElementById('editChkWillArt').checked;
    rec.chkWillStudy = document.getElementById('editChkWillStudy').checked;
    rec.chkWillExercise = document.getElementById('editChkWillExercise').checked;
    rec.chkWillBook = document.getElementById('editChkWillBook').checked;
    rec.chkWillVideo = document.getElementById('editChkWillVideo').checked;

    recomputeAllDailyRecords();
    updateOverallRecordForDate(rec.date);
    renderAll();
    document.getElementById('editDailyModal').style.display = 'none';
});

/***********************************
 * “晨曦之约” 功能实现 *
 ***********************************/
function addDawnRecord() {
    let date = formatDate(new Date());
    if (data.dawn.history.some(r => r.date === date)) {
        alert('今日已打卡，若需修改请编辑');
        return;
    }
    let sleptOnTime = document.getElementById('chkSleptOnTime').checked;
    let wokeOnTime = document.getElementById('chkWokeOnTime').checked;
    let specialCase = document.getElementById('chkSpecialCase').checked;
    let earlySleep = document.getElementById('chkEarlySleep').checked;
    let earlyRise = document.getElementById('chkEarlyRise').checked;

    let success = sleptOnTime || wokeOnTime || specialCase;
    data.dawn.history.push({ date, sleptOnTime, wokeOnTime, specialCase, earlySleep, earlyRise, success, willDelta: 0, staminaDelta: 0, consecutiveSuccess: 0, consecutiveFail: 0 });
    recomputeDawnConsecutive();
    updateOverallRecordForDate(date);
    renderAll();
}
document.getElementById('addDawnRecordBtn').addEventListener('click', addDawnRecord);

function recomputeDawnConsecutive() {
    data.dawn.consecutiveSuccess = 0;
    data.dawn.consecutiveFail = 0;
    let sorted = data.dawn.history.slice().sort((a,b) => a.date.localeCompare(b.date));
    sorted.forEach(rec => {
        if (rec.success) {
            data.dawn.consecutiveSuccess += 1;
            rec.consecutiveSuccess = data.dawn.consecutiveSuccess;
            data.dawn.consecutiveFail = 0;
            rec.consecutiveFail = 0;
        } else {
            data.dawn.consecutiveFail += 1;
            rec.consecutiveFail = data.dawn.consecutiveFail;
            data.dawn.consecutiveSuccess = 0;
            rec.consecutiveSuccess = 0;
        }
        let willDelta = 0, staminaDelta = 0;
        if (rec.success) {
            let cs = rec.consecutiveSuccess;
            if (cs >= 30) { willDelta += 2; staminaDelta += 1; }
            else if (cs >= 7) { willDelta += 1; staminaDelta += 0.5; }
            else if (cs >= 3) { willDelta += 0.5; staminaDelta += 0.2; }
            if (rec.earlySleep) willDelta += 1;
            if (rec.earlyRise) staminaDelta += 0.5;
        } else {
            if (!rec.sleptOnTime) willDelta -= 1;
            if (!rec.wokeOnTime) staminaDelta -= 0.5;
            let cf = rec.consecutiveFail;
            if (cf >= 7) willDelta -= 3;
            else if (cf >= 3) willDelta -= 2;
        }
        rec.willDelta = willDelta;
        rec.staminaDelta = staminaDelta;
    });
}

function renderDawnHistory() {
    let tbody = document.getElementById('dawnHistoryBody');
    tbody.innerHTML = '';
    let recs = data.dawn.history.slice().sort((a,b) => b.date.localeCompare(a.date));
    let totalPages = Math.ceil(recs.length / PAGE_SIZE);
    if (dawnHistoryPage > totalPages) dawnHistoryPage = totalPages || 1;
    let start = (dawnHistoryPage - 1) * PAGE_SIZE;
    let pageRecs = recs.slice(start, start + PAGE_SIZE);

    pageRecs.forEach(rec => {
        let tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${rec.date}</td>
            <td>${rec.success ? '成功' : '失败'}</td>
            <td>${rec.sleptOnTime ? '✓' : ''}</td>
            <td>${rec.wokeOnTime ? '✓' : ''}</td>
            <td>${rec.willDelta.toFixed(2)}</td>
            <td>${rec.staminaDelta.toFixed(2)}</td>
            <td>
                <button class="deleteDawnBtn" data-date="${rec.date}">删除</button>
                <button class="editDawnBtn" data-date="${rec.date}">编辑</button>
            </td>
        `;
        tbody.appendChild(tr);
    });

    let pag = document.getElementById('dawnHistoryPagination');
    pag.innerHTML = '';
    for (let i = 1; i <= totalPages; i++) {
        let btn = document.createElement('button');
        btn.innerText = i;
        if (i === dawnHistoryPage) btn.classList.add('active');
        btn.addEventListener('click', () => { dawnHistoryPage = i; renderDawnHistory(); });
        pag.appendChild(btn);
    }

    document.querySelectorAll('.deleteDawnBtn').forEach(btn => {
        btn.addEventListener('click', () => {
            let dt = btn.dataset.date;
            if (!confirm(`删除 ${dt} 的晨曦打卡？`)) return;
            data.dawn.history = data.dawn.history.filter(r => r.date !== dt);
            recomputeDawnConsecutive();
            data.overallRecords = data.overallRecords.filter(r => r.date !== dt);
            renderAll();
        });
    });
    document.querySelectorAll('.editDawnBtn').forEach(btn => {
        btn.addEventListener('click', () => openEditDawnModal(btn.dataset.date));
    });
}

document.getElementById('toggleDawnHistoryBtn').addEventListener('click', () => {
    let c = document.getElementById('dawnHistoryContainer');
    c.style.display = c.style.display === 'none' ? 'block' : 'none';
    if (c.style.display === 'block') renderDawnHistory();
});
document.getElementById('dawnHistoryContainer').style.display = 'none';

function openEditDawnModal(date) {
    let rec = data.dawn.history.find(r => r.date === date);
    if (!rec) return;
    document.getElementById('editDawnDate').value = date;
    document.getElementById('editChkSleptOnTime').checked = rec.sleptOnTime;
    document.getElementById('editChkWokeOnTime').checked = rec.wokeOnTime;
    document.getElementById('editChkSpecialCase').checked = rec.specialCase;
    document.getElementById('editChkEarlySleep').checked = rec.earlySleep;
    document.getElementById('editChkEarlyRise').checked = rec.earlyRise;
    document.getElementById('editDawnModal').style.display = 'flex';
}

document.getElementById('editDawnClose').addEventListener('click', () => {
    document.getElementById('editDawnModal').style.display = 'none';
});

document.getElementById('saveEditDawnBtn').addEventListener('click', () => {
    let date = document.getElementById('editDawnDate').value;
    let rec = data.dawn.history.find(r => r.date === date);
    if (!rec) return;
    rec.sleptOnTime = document.getElementById('editChkSleptOnTime').checked;
    rec.wokeOnTime = document.getElementById('editChkWokeOnTime').checked;
    rec.specialCase = document.getElementById('editChkSpecialCase').checked;
    rec.earlySleep = document.getElementById('editChkEarlySleep').checked;
    rec.earlyRise = document.getElementById('editChkEarlyRise').checked;

    recomputeDawnConsecutive();
    updateOverallRecordForDate(date);
    renderAll();
    document.getElementById('editDawnModal').style.display = 'none';
});

/*************************************
 * “每日个人总结” 功能实现 *
 *************************************/
function addDailySummary() {
    let date = document.getElementById('inputSummaryDate').value;
    if (!date) {
        alert('请选择日期');
        return;
    }
    if (data.summaries.some(s => s.date === date)) {
        alert('该日期已有总结，若需修改请删除后添加');
        return;
    }
    let content = document.getElementById('inputSummaryContent').value;
    data.summaries.push({ date, content });
    updateOverallRecordForDate(date);
    renderAll();
}
document.getElementById('addDailySummaryBtn').addEventListener('click', addDailySummary);

function renderSummaryHistory() {
    let tbody = document.getElementById('summaryHistoryBody');
    tbody.innerHTML = '';
    let recs = data.summaries.slice().sort((a,b) => b.date.localeCompare(a.date));
    let totalPages = Math.ceil(recs.length / PAGE_SIZE);
    if (summaryHistoryPage > totalPages) summaryHistoryPage = totalPages || 1;
    let start = (summaryHistoryPage - 1) * PAGE_SIZE;
    let pageRecs = recs.slice(start, start + PAGE_SIZE);

    pageRecs.forEach(rec => {
        let tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${rec.date}</td>
            <td>${rec.content}</td>
            <td>
                <button class="deleteSummaryBtn" data-date="${rec.date}">删除</button>
                <button class="editSummaryBtn" data-date="${rec.date}">编辑</button>
            </td>
        `;
        tbody.appendChild(tr);
    });

    let pag = document.getElementById('summaryHistoryPagination');
    pag.innerHTML = '';
    for (let i = 1; i <= totalPages; i++) {
        let btn = document.createElement('button');
        btn.innerText = i;
        if (i === summaryHistoryPage) btn.classList.add('active');
        btn.addEventListener('click', () => { summaryHistoryPage = i; renderSummaryHistory(); });
        pag.appendChild(btn);
    }

    document.querySelectorAll('.deleteSummaryBtn').forEach(btn => {
        btn.addEventListener('click', () => {
            let dt = btn.dataset.date;
            if (!confirm(`删除 ${dt} 的总结？`)) return;
            data.summaries = data.summaries.filter(r => r.date !== dt);
            data.overallRecords = data.overallRecords.filter(r => r.date !== dt);
            renderAll();
        });
    });
    document.querySelectorAll('.editSummaryBtn').forEach(btn => {
        btn.addEventListener('click', () => openEditSummaryModal(btn.dataset.date));
    });
}

document.getElementById('toggleSummaryHistoryBtn').addEventListener('click', () => {
    let c = document.getElementById('summaryHistoryContainer');
    c.style.display = c.style.display === 'none' ? 'block' : 'none';
    if (c.style.display === 'block') renderSummaryHistory();
});
document.getElementById('summaryHistoryContainer').style.display = 'none';

function openEditSummaryModal(date) {
    let rec = data.summaries.find(r => r.date === date);
    if (!rec) return;
    document.getElementById('editSummaryOldDate').value = date;
    document.getElementById('editSummaryDate').value = date;
    document.getElementById('editSummaryContent').value = rec.content;
    document.getElementById('editSummaryModal').style.display = 'flex';
}

document.getElementById('editSummaryClose').addEventListener('click', () => {
    document.getElementById('editSummaryModal').style.display = 'none';
});

document.getElementById('saveEditSummaryBtn').addEventListener('click', () => {
    let oldDate = document.getElementById('editSummaryOldDate').value;
    let rec = data.summaries.find(r => r.date === oldDate);
    if (!rec) return;
    let newDate = document.getElementById('editSummaryDate').value;
    let newContent = document.getElementById('editSummaryContent').value;
    rec.date = newDate;
    rec.content = newContent;
    updateOverallRecordForDate(oldDate);
    updateOverallRecordForDate(newDate);
    renderAll();
    document.getElementById('editSummaryModal').style.display = 'none';
});

/**************************************
 * “每日总记录” 功能实现 *
 **************************************/
function updateOverallRecordForDate(date) {
    let prevArt = data.initial.artExp;
    data.dailyRecords.filter(r => r.date < date).forEach(r => prevArt += r.deltas.artExpDelta);
    data.tasks.forEach(task => {
        if (task.status === 'completed') {
            task.rewards.forEach(rw => {
                if (rw.type === 'artExp') prevArt += rw.value;
            });
        }
        if (task.status === 'failed') {
            task.penalties.forEach(pn => {
                if (pn.type === 'artExp') prevArt -= pn.value;
            });
        }
    });
    let currArt = calculateAttributes().artExp;
    let artDelta = currArt - prevArt;

    let prevKn = data.initial.knowledge;
    data.dailyRecords.filter(r => r.date < date).forEach(r => prevKn += r.deltas.knowledgeDelta);
    data.tasks.forEach(task => {
        if (task.status === 'completed') {
            task.rewards.forEach(rw => {
                if (rw.type === 'knowledge') prevKn += rw.value;
            });
        }
        if (task.status === 'failed') {
            task.penalties.forEach(pn => {
                if (pn.type === 'knowledge') prevKn -= pn.value;
            });
        }
    });
    let currKn = calculateAttributes().knowledge;
    let knDelta = currKn - prevKn;

    let prevIt = data.initial.intelligence;
    data.dailyRecords.filter(r => r.date < date).forEach(r => prevIt += r.deltas.intDelta);
    data.tasks.forEach(task => {
        if (task.status === 'completed') {
            task.rewards.forEach(rw => {
                if (rw.type === 'intelligence') prevIt += rw.value;
            });
        }
        if (task.status === 'failed') {
            task.penalties.forEach(pn => {
                if (pn.type === 'intelligence') prevIt -= pn.value;
            });
        }
    });
    let currIt = calculateAttributes().intelligence;
    let itDelta = currIt - prevIt;

    let dawnRec = data.dawn.history.find(r => r.date === date);
    let dawnRes = dawnRec ? (dawnRec.success ? '成功' : '失败') : '无';

    let sumRec = data.summaries.find(r => r.date === date);
    let sumContent = sumRec ? sumRec.content : '';

    let pendingCount = data.tasks.filter(t => t.status === 'pending').length;
    let tasksOverview = `待完成：${pendingCount} 个`;

    let existing = data.overallRecords.find(r => r.date === date);
    if (existing) {
        existing.artProgress = { current: currArt, delta: artDelta };
        existing.truthKnowledge = { current: currKn, delta: knDelta };
        existing.truthIntelligence = { current: currIt, delta: itDelta };
        existing.dawn = dawnRes;
        existing.summary = sumContent;
        existing.tasksOverview = tasksOverview;
    } else {
        data.overallRecords.push({
            date,
            artProgress: { current: currArt, delta: artDelta },
            truthKnowledge: { current: currKn, delta: knDelta },
            truthIntelligence: { current: currIt, delta: itDelta },
            dawn: dawnRes,
            summary: sumContent,
            tasksOverview
        });
    }
}

function renderOverallHistory() {
    let tbody = document.getElementById('overallHistoryBody');
    tbody.innerHTML = '';
    let recs = data.overallRecords.slice().sort((a,b) => b.date.localeCompare(a.date));
    let totalPages = Math.ceil(recs.length / PAGE_SIZE);
    if (overallHistoryPage > totalPages) overallHistoryPage = totalPages || 1;
    let start = (overallHistoryPage - 1) * PAGE_SIZE;
    let pageRecs = recs.slice(start, start + PAGE_SIZE);

    pageRecs.forEach(rec => {
        let tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${rec.date}</td>
            <td>${rec.artProgress.current.toFixed(2)}/${rec.artProgress.delta.toFixed(2)}</td>
            <td>${rec.truthKnowledge.current.toFixed(2)}/${rec.truthKnowledge.delta.toFixed(2)}</td>
            <td>${rec.truthIntelligence.current.toFixed(2)}/${rec.truthIntelligence.delta.toFixed(2)}</td>
            <td>${rec.dawn}</td>
            <td>${rec.summary}</td>
            <td>${rec.tasksOverview}</td>
            <td><button class="deleteOverallBtn" data-date="${rec.date}">删除</button></td>
        `;
        tbody.appendChild(tr);
    });

    let pag = document.getElementById('overallHistoryPagination');
    pag.innerHTML = '';
    for (let i = 1; i <= totalPages; i++) {
        let btn = document.createElement('button');
        btn.innerText = i;
        if (i === overallHistoryPage) btn.classList.add('active');
        btn.addEventListener('click', () => { overallHistoryPage = i; renderOverallHistory(); });
        pag.appendChild(btn);
    }

    document.querySelectorAll('.deleteOverallBtn').forEach(btn => {
        btn.addEventListener('click', () => {
            let dt = btn.dataset.date;
            if (!confirm(`删除 ${dt} 总记录？`)) return;
            data.overallRecords = data.overallRecords.filter(r => r.date !== dt);
            data.dailyRecords = data.dailyRecords.filter(r => r.date !== dt);
            data.dawn.history = data.dawn.history.filter(r => r.date !== dt);
            data.summaries = data.summaries.filter(r => r.date !== dt);
            renderAll();
        });
    });
}

document.getElementById('toggleOverallHistoryBtn').addEventListener('click', () => {
    let c = document.getElementById('overallHistoryContainer');
    c.style.display = c.style.display === 'none' ? 'block' : 'none';
    if (c.style.display === 'block') renderOverallHistory();
});
document.getElementById('overallHistoryContainer').style.display = 'none';

/***********************************
 * “幻构师计划” 渲染逻辑（示例） *
 ***********************************/
function renderArtPlan() {
    let tbody = document.getElementById('artPlanTable').querySelector('tbody');
    tbody.innerHTML = '';
    let attrs = calculateAttributes();
    let currArtExp = attrs.artExp;

    ART_LEVELS.forEach(lvlInfo => {
        let tr = document.createElement('tr');
        let stageName = '未达成';
        lvlInfo.stages.forEach(st => {
            if (currArtExp >= st.min && currArtExp <= st.max) stageName = st.name;
        });
        if (currArtExp > lvlInfo.totalReq) stageName = '高级';
        let progressPercent = Math.min(100, (currArtExp / lvlInfo.totalReq) * 100).toFixed(2);
        tr.innerHTML = `
            <td>${lvlInfo.level}</td>
            <td>${lvlInfo.totalReq}</td>
            <td>${stageName}</td>
            <td>${currArtExp.toFixed(2)}</td>
            <td>
                <div class="progress-container">
                    <div class="progress-bar" style="width: ${progressPercent}%;"></div>
                </div>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

/**************************************
 * “真理之路计划” 渲染逻辑（示例） *
 **************************************/
function renderTruthPlan() {
    let knTbody = document.getElementById('truthKnowledgeTable').querySelector('tbody');
    let itTbody = document.getElementById('truthIntelligenceTable').querySelector('tbody');
    knTbody.innerHTML = ''; itTbody.innerHTML = '';
    let attrs = calculateAttributes();
    let currKn = attrs.knowledge, currIt = attrs.intelligence;

    TRUTH_KNOWLEDGE_LEVELS.forEach(lvlInfo => {
        let stageName = '未达成';
        lvlInfo.stages.forEach(st => {
            if (currKn >= st.min && currKn <= st.max) stageName = st.name;
        });
        if (currKn > lvlInfo.totalReq) stageName = '高级';
        let progressPercent = Math.min(100, (currKn / lvlInfo.totalReq) * 100).toFixed(2);
        let tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${lvlInfo.level}</td>
            <td>${lvlInfo.totalReq}</td>
            <td>${stageName}</td>
            <td>${currKn.toFixed(2)}</td>
            <td>
                <div class="progress-container">
                    <div class="progress-bar" style="width: ${progressPercent}%;"></div>
                </div>
            </td>
        `;
        knTbody.appendChild(tr);
    });

    TRUTH_INTELLIGENCE_LEVELS.forEach(lvlInfo => {
        let stageName = '未达成';
        lvlInfo.stages.forEach(st => {
            if (currIt >= st.min && currIt <= st.max) stageName = st.name;
        });
        if (currIt > lvlInfo.totalReq) stageName = '高级';
        let progressPercent = Math.min(100, (currIt / lvlInfo.totalReq) * 100).toFixed(2);
        let tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${lvlInfo.level}</td>
            <td>${lvlInfo.totalReq}</td>
            <td>${stageName}</td>
            <td>${currIt.toFixed(2)}</td>
            <td>
                <div class="progress-container">
                    <div class="progress-bar" style="width: ${progressPercent}%;"></div>
                </div>
            </td>
        `;
        itTbody.appendChild(tr);
    });
}

/***********************************
 * “称号系统” 渲染逻辑（示例） *
 ***********************************/
function renderTitles() {
    let attrs = calculateAttributes();
    let currDawnDays = data.dawn.consecutiveSuccess;
    let currReadExp = attrs.readExp;
    let currWill = attrs.will;
    let currCha = attrs.charisma;

    // 渲染“晨曦之约称号”
    let dawnTbody = document.getElementById('titlesDawnTable').querySelector('tbody');
    dawnTbody.innerHTML = '';
    TITLES_DAWN.forEach(t => {
        let achieved = currDawnDays >= t.reqDays;
        let progressPercent = Math.min(100, (currDawnDays / t.reqDays) * 100).toFixed(2);
        let tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${t.level}</td>
            <td>${t.name}</td>
            <td>${t.reqDays}</td>
            <td>${currDawnDays}</td>
            <td>${achieved ? '已达成' : ''}</td>
            <td>
                <div class="progress-container">
                    <div class="progress-bar" style="width: ${progressPercent}%;"></div>
                </div>
            </td>
        `;
        dawnTbody.appendChild(tr);
    });

    // 渲染“阅识称号”
    let readTbody = document.getElementById('titlesReadExpTable').querySelector('tbody');
    readTbody.innerHTML = '';
    TITLES_READ_EXP.forEach(t => {
        let achieved = currReadExp >= t.req;
        let progressPercent = Math.min(100, (currReadExp / t.req) * 100).toFixed(2);
        let tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${t.level}</td>
            <td>${t.name}</td>
            <td>${t.req}</td>
            <td>${currReadExp.toFixed(2)}</td>
            <td>${achieved ? '已达成' : ''}</td>
            <td>
                <div class="progress-container">
                    <div class="progress-bar" style="width: ${progressPercent}%;"></div>
                </div>
            </td>
        `;
        readTbody.appendChild(tr);
    });

    // 渲染“意志称号”
    let willTbody = document.getElementById('titlesWillTable').querySelector('tbody');
    willTbody.innerHTML = '';
    TITLES_WILL.forEach(t => {
        let achieved = currWill >= t.req;
        let progressPercent = Math.min(100, (currWill / t.req) * 100).toFixed(2);
        let tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${t.level}</td>
            <td>${t.name}</td>
            <td>${t.req}</td>
            <td>${currWill.toFixed(2)}</td>
            <td>${achieved ? '已达成' : ''}</td>
            <td>
                <div class="progress-container">
                    <div class="progress-bar" style="width: ${progressPercent}%;"></div>
                </div>
            </td>
        `;
        willTbody.appendChild(tr);
    });

    // 渲染“魅力称号”
    let chaTbody = document.getElementById('titlesCharismaTable').querySelector('tbody');
    chaTbody.innerHTML = '';
    TITLES_CHARISMA.forEach(t => {
        let achieved = currCha >= t.req;
        let progressPercent = Math.min(100, (currCha / t.req) * 100).toFixed(2);
        let tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${t.level}</td>
            <td>${t.name}</td>
            <td>${t.req}</td>
            <td>${currCha.toFixed(2)}</td>
            <td>${achieved ? '已达成' : ''}</td>
            <td>
                <div class="progress-container">
                    <div class="progress-bar" style="width: ${progressPercent}%;"></div>
                </div>
            </td>
        `;
        chaTbody.appendChild(tr);
    });
}

/***********************************
 * “任务系统” 功能实现（示例） *
 ***********************************/
function addTask() {
    let name = document.getElementById('taskName').value.trim();
    if (!name) { alert('请输入任务名称'); return; }
    let description = document.getElementById('taskDescription').value.trim();
    let type = document.getElementById('taskType').value;
    let cycle = document.getElementById('taskCycle').value;
    let targetType = document.getElementById('taskTargetType').value;
    let targetValue = parseFloat(document.getElementById('taskTargetValue').value);
    let deadline = document.getElementById('taskDeadline').value;

    // 任务对象结构示例
    let task = {
        id: Date.now().toString(),
        name,
        description,
        type,
        cycle,
        targetType,
        targetValue,
        deadline,
        status: 'pending',
        rewards: [],
        penalties: [],
        creationDate: formatDate(new Date()),
        completionDate: null
    };
    data.tasks.push(task);
    renderTasks();
    renderAll();
}
document.getElementById('createTaskBtn').addEventListener('click', addTask);

function renderTasks() {
    // 未完成任务
    let pendingBody = document.getElementById('pendingTasksBody');
    pendingBody.innerHTML = '';
    data.tasks.filter(t => t.status === 'pending').forEach(task => {
        let daysLeft = task.deadline ? (daysBetween(parseDate(formatDate(new Date())), parseDate(task.deadline)) - 1) : '--';
        let progressPercent = 0; // 可根据 task.targetType 等计算进度
        let tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${task.creationDate}</td>
            <td>${task.name}</td>
            <td>${task.type}</td>
            <td>${progressPercent}%</td>
            <td>${daysLeft}</td>
            <td>--</td>
            <td>
                <div class="progress-container">
                    <div class="progress-bar" style="width: ${progressPercent}%;"></div>
                </div>
            </td>
            <td>
                <button class="editTaskBtn" data-id="${task.id}">编辑</button>
                <button class="deleteTaskBtn" data-id="${task.id}">删除</button>
            </td>
        `;
        pendingBody.appendChild(tr);
    });

    // 已完成/失败任务
    let completedBody = document.getElementById('completedTasksBody');
    completedBody.innerHTML = '';
    data.tasks.filter(t => t.status !== 'pending').forEach(task => {
        let tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${task.creationDate}</td>
            <td>${task.name}</td>
            <td>${task.type}</td>
            <td>${task.targetValue}/${task.targetValue}</td>
            <td>${task.completionDate || '--'}</td>
            <td>--</td>
            <td>${task.status}</td>
            <td>
                <button class="editTaskBtn" data-id="${task.id}">编辑</button>
                <button class="deleteTaskBtn" data-id="${task.id}">删除</button>
            </td>
        `;
        completedBody.appendChild(tr);
    });

    // 绑定编辑/删除事件
    document.querySelectorAll('.deleteTaskBtn').forEach(btn => {
        btn.addEventListener('click', () => {
            let id = btn.dataset.id;
            if (!confirm('确认删除该任务？')) return;
            data.tasks = data.tasks.filter(t => t.id !== id);
            renderTasks();
            renderAll();
        });
    });
    document.querySelectorAll('.editTaskBtn').forEach(btn => {
        btn.addEventListener('click', () => openEditTaskModal(btn.dataset.id));
    });
}

function openEditTaskModal(id) {
    let task = data.tasks.find(t => t.id === id);
    if (!task) return;
    document.getElementById('editTaskId').value = id;
    document.getElementById('editTaskName').value = task.name;
    document.getElementById('editTaskDescription').value = task.description;
    document.getElementById('editTaskType').value = task.type;
    document.getElementById('editTaskCycle').value = task.cycle;
    document.getElementById('editTaskTargetType').value = task.targetType;
    document.getElementById('editTaskTargetValue').value = task.targetValue;
    document.getElementById('editTaskDeadline').value = task.deadline;
    // 此处省略惩罚和奖励列表渲染
    document.getElementById('editTaskStatus').value = task.status;
    document.getElementById('editTaskCompletionDate').value = task.completionDate;
    document.getElementById('editTaskModal').style.display = 'flex';
}

document.getElementById('editTaskClose').addEventListener('click', () => {
    document.getElementById('editTaskModal').style.display = 'none';
});

document.getElementById('saveEditTaskBtn').addEventListener('click', () => {
    let id = document.getElementById('editTaskId').value;
    let task = data.tasks.find(t => t.id === id);
    if (!task) return;
    task.name = document.getElementById('editTaskName').value.trim();
    task.description = document.getElementById('editTaskDescription').value.trim();
    task.type = document.getElementById('editTaskType').value;
    task.cycle = document.getElementById('editTaskCycle').value;
    task.targetType = document.getElementById('editTaskTargetType').value;
    task.targetValue = parseFloat(document.getElementById('editTaskTargetValue').value);
    task.deadline = document.getElementById('editTaskDeadline').value;
    task.status = document.getElementById('editTaskStatus').value;
    task.completionDate = document.getElementById('editTaskCompletionDate').value;
    // 更新惩罚/奖励列表省略
    renderTasks();
    renderAll();
    document.getElementById('editTaskModal').style.display = 'none';
});

/***********************************
 * 其他渲染入口 *
 ***********************************/
function renderAll() {
    renderCurrentStatus();
    renderDailyHistory();
    renderDawnHistory();
    renderSummaryHistory();
    renderOverallHistory();
    renderArtPlan();
    renderTruthPlan();
    renderTitles();
    renderTasks();
}

// 初次加载已有数据后执行
loadData();
renderAll();

    </script>
</body>
</html>
